var Vr = Object.defineProperty;
var Dr = (e, t, r) => t in e ? Vr(e, t, { enumerable: !0, configurable: !0, writable: !0, value: r }) : e[t] = r;
var D = (e, t, r) => Dr(e, typeof t != "symbol" ? t + "" : t, r);
import { Disposable as Re, UniverInstanceType as Y, toDisposable as Or, IUniverInstanceService as de, Inject as M, LocaleService as ce, DataValidationErrorStyle as lt, DataValidationStatus as Xe, ICommandService as Q, Injector as ye, IConfigService as an, DataValidationRenderMode as se, DisposableCollection as Tr, dayjs as wn, numfmt as $n, CellValueType as Pr, CommandType as rt, DataValidationType as H, ObjectMatrix as It, Range as Ar, Rectangle as Rt, queryObjectMatrix as Zt, Optional as Lr, RxDisposable as sn, InterceptorEffectEnum as Hn, sequenceExecute as jn, bufferDebounceTime as kr, debounce as Nr, UndoCommand as Ur, RedoCommand as Fr, isUnitRangesEqual as xr, shallowEqual as In, ThemeService as Yn, ColorKit as Br, isFormulaString as De, generateRandomId as Rn, VerticalAlign as _e, HorizontalAlign as Ze, DEFAULT_STYLES as ae, WrapStrategy as fe, Plugin as Xn, merge as Zn, DependentOn as Wr } from "@univerjs/core";
import { DeviceInputEventType as gt, IRenderManagerService as Fe, fixLineWidthByScale as yn, Transform as $r, CheckboxShape as Hr, getCurrentTypeOfRenderer as Ke, CURSOR_TYPE as ze, Shape as jr, Rect as Kn, FontCache as Yr, getFontStyleString as Oe, DocSimpleSkeleton as it, Text as bn } from "@univerjs/engine-render";
import { SheetInterceptorService as ln, VALIDATE_CELL as Xr, SheetsSelectionsService as zn, SetRangeValuesCommand as st, getSheetCommandTarget as Gn, rangeToDiscreteRange as Zr, SheetPermissionCheckController as Kr, WorksheetEditPermission as Kt, WorksheetSetCellStylePermission as zt, RangeProtectionPermissionEditPoint as Gt, WorkbookEditablePermission as qt, INTERCEPTOR_POINT as qn, InterceptCellContentPriority as Jn, checkRangesEditablePermission as zr } from "@univerjs/sheets";
import { SheetDataValidationModel as ie, SheetsDataValidationValidatorService as Gr, getDataValidationCellValue as Nt, getCellValueOrigin as re, serializeListOptions as Qn, createDefaultNewRule as er, AddSheetDataValidationCommand as dn, DATA_VALIDATION_PLUGIN_NAME as tr, getDataValidationDiffMutations as Jt, UpdateSheetDataValidationRangeCommand as nr, DataValidationCacheService as rr, UpdateSheetDataValidationSettingCommand as Mn, UpdateSheetDataValidationOptionsCommand as qr, RemoveSheetDataValidationCommand as ir, RemoveSheetAllDataValidationCommand as Jr, DataValidationFormulaController as Qr, deserializeListOptions as ei, CUSTOM_FORMULA_INPUT_NAME as or, BASE_FORMULA_INPUT_NAME as Pt, LIST_FORMULA_INPUT_NAME as cn, CHECKBOX_FORMULA_INPUT_NAME as ar, DataValidationFormulaService as ti, CHECKBOX_FORMULA_2 as ni, CHECKBOX_FORMULA_1 as ri, getFormulaResult as Ut, isLegalFormulaResult as En, transformCheckboxValue as ii, UniverSheetsDataValidationPlugin as oi } from "@univerjs/sheets-data-validation";
import { DataValidatorRegistryService as be, DataValidatorDropdownType as ge, DataValidationModel as un, DataValidatorRegistryScope as ai, TWO_FORMULA_OPERATOR_COUNT as si, getRuleOptions as Vn, getRuleSetting as Dn } from "@univerjs/data-validation";
import { ISidebarService as sr, IDialogService as li, IZenZoneService as lr, KeyCode as vt, MenuItemType as hn, getMenuHiddenObservable as di, RibbonDataGroup as ci, IMenuManagerService as ui, useDependency as F, ComponentManager as pn, useObservable as Pe, useEvent as Qt, useSidebarClick as dr } from "@univerjs/ui";
import { filter as gn, BehaviorSubject as On, distinctUntilChanged as hi, Subject as pi, debounceTime as cr, bufferTime as ur, of as gi } from "rxjs";
import { getPatternType as vi } from "@univerjs/sheets-numfmt";
import { ISheetCellDropdownManagerService as fi, IEditorBridgeService as hr, SetCellEditVisibleOperation as ft, HoverManagerService as mi, CellAlertManagerService as _i, CellAlertType as Si, IAutoFillService as Ci, APPLY_TYPE as ot, virtualizeDiscreteRanges as en, getAutoFillRepeatRange as wi, ISheetClipboardService as Ii, COPY_TYPE as Tn, PREDEFINED_HOOK_NAME as mt, getRepeatRange as Pn, getCurrentRangeDisable$ as Ri, AutoHeightController as pr, SheetSkeletonManagerService as dt, IMarkSelectionService as yi } from "@univerjs/sheets-ui";
import { Button as Ge, FormLayout as j, RadioGroup as vn, Radio as Ae, Checkbox as At, Input as Le, Select as An, clsx as Ne, borderClassName as yt, DraggableList as bi, Dropdown as Mi } from "@univerjs/design";
import { createElement as Ie, forwardRef as xe, useRef as pt, useState as B, useMemo as ct, useEffect as ke } from "react";
import { jsxs as $, Fragment as ut, jsx as S } from "react/jsx-runtime";
import { deserializeRangeWithSheet as Ei, serializeRange as gr } from "@univerjs/engine-formula";
import { RangeSelector as Vi, FormulaEditor as vr } from "@univerjs/sheets-formula-ui";
var Di = Object.getOwnPropertyDescriptor, Oi = (e, t, r, n) => {
  for (var i = n > 1 ? void 0 : n ? Di(t, r) : t, o = e.length - 1, a; o >= 0; o--)
    (a = e[o]) && (i = a(i) || i);
  return i;
}, Ln = (e, t) => (r, n) => t(r, n, e);
let Se = class extends Re {
  constructor(t, r) {
    super();
    D(this, "_open$", new On(!1));
    D(this, "open$", this._open$.pipe(hi()));
    D(this, "_activeRule");
    D(this, "_activeRule$", new On(void 0));
    D(this, "activeRule$", this._activeRule$.asObservable());
    D(this, "_closeDisposable", null);
    this._univerInstanceService = t, this._sidebarService = r, this.disposeWithMe(
      this._univerInstanceService.getCurrentTypeOfUnit$(Y.UNIVER_SHEET).pipe(gn((n) => !n)).subscribe(() => {
        this.close();
      })
    ), this.disposeWithMe(this._sidebarService.sidebarOptions$.subscribe((n) => {
      n.id === Mt && (n.visible || setTimeout(() => {
        this._sidebarService.sidebarOptions$.next({ visible: !1 });
      }));
    }));
  }
  get activeRule() {
    return this._activeRule;
  }
  get isOpen() {
    return this._open$.getValue();
  }
  dispose() {
    var t;
    super.dispose(), this._open$.next(!1), this._open$.complete(), this._activeRule$.complete(), (t = this._closeDisposable) == null || t.dispose();
  }
  open() {
    this._open$.next(!0);
  }
  close() {
    var t;
    this._open$.next(!1), (t = this._closeDisposable) == null || t.dispose();
  }
  setCloseDisposable(t) {
    this._closeDisposable = Or(() => {
      t.dispose(), this._closeDisposable = null;
    });
  }
  setActiveRule(t) {
    this._activeRule = t, this._activeRule$.next(t);
  }
};
Se = Oi([
  Ln(0, de),
  Ln(1, sr)
], Se);
const Te = "#ECECEC", fn = "sheets-data-validation-ui.config", bt = {};
var Ti = Object.getOwnPropertyDescriptor, Pi = (e, t, r, n) => {
  for (var i = n > 1 ? void 0 : n ? Ti(t, r) : t, o = e.length - 1, a; o >= 0; o--)
    (a = e[o]) && (i = a(i) || i);
  return i;
}, $e = (e, t) => (r, n) => t(r, n, e);
let qe = class extends Re {
  constructor(e, t, r, n, i, o) {
    super(), this._sheetInterceptorService = e, this._dataValidationModel = t, this._dataValidatorRegistryService = r, this._dialogService = n, this._localeService = i, this._sheetsDataValidationValidatorService = o, this._initEditorBridgeInterceptor();
  }
  _initEditorBridgeInterceptor() {
    this._sheetInterceptorService.writeCellInterceptor.intercept(
      Xr,
      {
        handler: async (e, t, r) => {
          const n = await e, { row: i, col: o, unitId: a, subUnitId: l } = t, s = this._dataValidationModel.getRuleIdByLocation(a, l, i, o), d = s ? this._dataValidationModel.getRuleById(a, l, s) : void 0;
          if (n === !1)
            return r(Promise.resolve(!1));
          if (!d || d.errorStyle !== lt.STOP)
            return r(Promise.resolve(!0));
          const c = this._dataValidatorRegistryService.getValidatorItem(d.type);
          return !c || await this._sheetsDataValidationValidatorService.validatorCell(a, l, i, o) === Xe.VALID ? r(Promise.resolve(!0)) : (this._dialogService.open({
            width: 368,
            title: {
              title: this._localeService.t("dataValidation.alert.title")
            },
            id: "reject-input-dialog",
            children: {
              title: c.getRuleFinalError(d, { row: i, col: o, unitId: a, subUnitId: l })
            },
            footer: {
              title: Ie(
                Ge,
                {
                  variant: "primary",
                  onClick: () => this._dialogService.close("reject-input-dialog")
                },
                this._localeService.t("dataValidation.alert.ok")
              )
            },
            onClose: () => {
              this._dialogService.close("reject-input-dialog");
            }
          }), r(Promise.resolve(!1)));
        }
      }
    );
  }
  showReject(e) {
    this._dialogService.open({
      width: 368,
      title: {
        title: this._localeService.t("dataValidation.alert.title")
      },
      id: "reject-input-dialog",
      children: {
        title: e
      },
      footer: {
        title: Ie(
          Ge,
          {
            variant: "primary",
            onClick: () => this._dialogService.close("reject-input-dialog")
          },
          this._localeService.t("dataValidation.alert.ok")
        )
      },
      onClose: () => {
        this._dialogService.close("reject-input-dialog");
      }
    });
  }
};
qe = Pi([
  $e(0, M(ln)),
  $e(1, M(ie)),
  $e(2, M(be)),
  $e(3, li),
  $e(4, M(ce)),
  $e(5, M(Gr))
], qe);
var Ai = Object.getOwnPropertyDescriptor, Li = (e, t, r, n) => {
  for (var i = n > 1 ? void 0 : n ? Ai(t, r) : t, o = e.length - 1, a; o >= 0; o--)
    (a = e[o]) && (i = a(i) || i);
  return i;
}, he = (e, t) => (r, n) => t(r, n, e);
const Ft = (e) => {
  if (e == null || typeof e == "boolean")
    return;
  if (typeof e == "number" || !Number.isNaN(+e))
    return wn($n.format("yyyy-MM-dd HH:mm:ss", Number(e)));
  const t = wn(e);
  if (t.isValid())
    return t;
};
function ki(e, t) {
  const r = vi(t);
  if (e === r)
    return t;
  switch (e) {
    case "datetime":
      return "yyyy-MM-dd hh:mm:ss";
    case "date":
      return "yyyy-MM-dd";
    case "time":
      return "HH:mm:ss";
  }
}
let Ue = class extends Re {
  constructor(t, r, n, i, o, a, l, s, d, c, h) {
    super();
    D(this, "_activeDropdown");
    D(this, "_activeDropdown$", new pi());
    D(this, "_currentPopup", null);
    D(this, "activeDropdown$", this._activeDropdown$.asObservable());
    D(this, "_zenVisible", !1);
    this._univerInstanceService = t, this._dataValidatorRegistryService = r, this._zenZoneService = n, this._dataValidationModel = i, this._sheetsSelectionsService = o, this._cellDropdownManagerService = a, this._sheetDataValidationModel = l, this._commandService = s, this._editorBridgeService = d, this._injector = c, this._configService = h, this._init(), this._initSelectionChange(), this.disposeWithMe(() => {
      this._activeDropdown$.complete();
    });
  }
  get activeDropdown() {
    return this._activeDropdown;
  }
  _init() {
    this.disposeWithMe(this._zenZoneService.visible$.subscribe((t) => {
      this._zenVisible = t, t && this.hideDropdown();
    }));
  }
  _getDropdownByCell(t, r, n, i) {
    const o = t ? this._univerInstanceService.getUnit(t, Y.UNIVER_SHEET) : this._univerInstanceService.getCurrentUnitForType(Y.UNIVER_SHEET);
    if (!o)
      return;
    const a = r ? o.getSheetBySheetId(r) : o.getActiveSheet();
    if (!a)
      return;
    const l = this._dataValidationModel.getRuleByLocation(o.getUnitId(), a.getSheetId(), n, i);
    if (!l)
      return;
    const s = this._dataValidatorRegistryService.getValidatorItem(l.type);
    return s == null ? void 0 : s.dropdownType;
  }
  _initSelectionChange() {
    this.disposeWithMe(this._sheetsSelectionsService.selectionMoveEnd$.subscribe((t) => {
      t && t.every((r) => !(r.primary && this._getDropdownByCell(r.primary.unitId, r.primary.sheetId, r.primary.actualRow, r.primary.actualColumn))) && this.hideDropdown();
    }));
  }
  // eslint-disable-next-line max-lines-per-function, complexity
  showDropdown(t) {
    var b, y, g, C;
    const { location: r } = t, { row: n, col: i, unitId: o, subUnitId: a, workbook: l, worksheet: s } = r;
    if (this._currentPopup && this._currentPopup.dispose(), this._zenVisible)
      return;
    this._activeDropdown = t, this._activeDropdown$.next(this._activeDropdown);
    const d = this._sheetDataValidationModel.getRuleByLocation(o, a, n, i);
    if (!d)
      return;
    const c = this._dataValidatorRegistryService.getValidatorItem(d.type);
    if (!(c != null && c.dropdownType))
      return;
    let h;
    const u = async (f, I) => {
      var O, P, k;
      if (!f)
        return !0;
      const w = f, _ = s.getCell(n, i), R = w.format(I === "date" ? "YYYY-MM-DD 00:00:00" : "YYYY-MM-DD HH:mm:ss"), L = (O = $n.parseDate(R)) == null ? void 0 : O.v, N = I === "time" ? L % 1 : L, E = l.getStyles().getStyleByCell(_), T = (k = (P = E == null ? void 0 : E.n) == null ? void 0 : P.pattern) != null ? k : "";
      return d.errorStyle !== lt.STOP || await c.validator({
        value: N,
        unitId: o,
        subUnitId: a,
        row: n,
        column: i,
        worksheet: s,
        workbook: l,
        interceptValue: R.replace("Z", "").replace("T", " "),
        t: Pr.NUMBER
      }, d) ? (await this._commandService.executeCommand(st.id, {
        unitId: o,
        subUnitId: a,
        range: {
          startColumn: i,
          endColumn: i,
          startRow: n,
          endRow: n
        },
        value: {
          v: N,
          t: 2,
          p: null,
          f: null,
          si: null,
          s: {
            n: {
              pattern: ki(I, T)
            }
          }
        }
      }), await this._commandService.executeCommand(ft.id, {
        visible: !1,
        eventType: gt.Keyboard,
        unitId: o,
        keycode: vt.ESC
      }), !0) : (this._injector.has(qe) && this._injector.get(qe).showReject(c.getRuleFinalError(d, { row: n, col: i, unitId: o, subUnitId: a })), !1);
    };
    let v;
    switch (c.dropdownType) {
      case ge.DATE: {
        const f = re(s.getCellRaw(n, i)), I = Ft(f), w = !!((b = d.bizInfo) != null && b.showTime);
        v = {
          location: r,
          type: "datepicker",
          props: {
            showTime: w,
            onChange: (_) => u(_, w ? "datetime" : "date"),
            defaultValue: I,
            patternType: "date"
          }
        };
        break;
      }
      case ge.TIME: {
        const f = re(s.getCellRaw(n, i)), I = Ft(f);
        v = {
          location: r,
          type: "datepicker",
          props: {
            onChange: (w) => u(w, "time"),
            defaultValue: I,
            patternType: "time"
          }
        };
        break;
      }
      case ge.DATETIME: {
        const f = re(s.getCellRaw(n, i)), I = Ft(f);
        v = {
          location: r,
          type: "datepicker",
          props: {
            onChange: (w) => u(w, "datetime"),
            defaultValue: I,
            patternType: "datetime"
          }
        };
        break;
      }
      case ge.LIST:
      case ge.MULTIPLE_LIST: {
        const f = c.dropdownType === ge.MULTIPLE_LIST, I = async (E) => {
          const T = Qn(E), O = {
            unitId: o,
            subUnitId: a,
            range: {
              startColumn: i,
              endColumn: i,
              startRow: n,
              endRow: n
            },
            value: {
              v: T,
              p: null,
              f: null,
              si: null
            }
          };
          return this._commandService.executeCommand(st.id, O), this._editorBridgeService.isVisible().visible && await this._commandService.executeCommand(ft.id, {
            visible: !1,
            eventType: gt.Keyboard,
            unitId: o,
            keycode: vt.ESC
          }), !f;
        }, w = (d == null ? void 0 : d.renderMode) === se.CUSTOM || (d == null ? void 0 : d.renderMode) === void 0, _ = c.getListWithColor(d, o, a), R = Nt(s.getCellRaw(n, i)), L = () => {
          this._commandService.executeCommand(Be.id, {
            ruleId: d.uid
          }), h == null || h.dispose();
        }, N = _.map((E) => ({
          label: E.label,
          value: E.label,
          color: w || E.color ? E.color || Te : "transparent"
        }));
        v = {
          location: r,
          type: "list",
          props: {
            onChange: (E) => I(E),
            options: N,
            onEdit: L,
            defaultValue: R,
            multiple: f,
            showEdit: (g = (y = this._configService.getConfig(fn)) == null ? void 0 : y.showEditOnDropdown) != null ? g : !0
          }
        };
        break;
      }
      case ge.CASCADE: {
        v = {
          type: "cascader",
          props: {
            onChange: (I) => {
              const w = {
                unitId: o,
                subUnitId: a,
                range: {
                  startColumn: i,
                  endColumn: i,
                  startRow: n,
                  endRow: n
                },
                value: {
                  v: I.join("/"),
                  p: null,
                  f: null,
                  si: null
                }
              };
              return this._commandService.syncExecuteCommand(st.id, w), this._editorBridgeService.isVisible().visible && this._commandService.syncExecuteCommand(ft.id, {
                visible: !1,
                eventType: gt.Keyboard,
                unitId: o,
                keycode: vt.ESC
              }), !0;
            },
            defaultValue: Nt(s.getCellRaw(n, i)).split("/"),
            options: JSON.parse((C = d.formula1) != null ? C : "[]")
          },
          location: r
        };
        break;
      }
      case ge.COLOR: {
        v = {
          type: "color",
          props: {
            onChange: (I) => {
              const w = {
                unitId: o,
                subUnitId: a,
                range: {
                  startColumn: i,
                  endColumn: i,
                  startRow: n,
                  endRow: n
                },
                value: {
                  v: I,
                  p: null,
                  f: null,
                  si: null
                }
              };
              return this._commandService.syncExecuteCommand(st.id, w), this._editorBridgeService.isVisible().visible && this._commandService.syncExecuteCommand(ft.id, {
                visible: !1,
                eventType: gt.Keyboard,
                unitId: o,
                keycode: vt.ESC
              }), !0;
            },
            defaultValue: Nt(s.getCellRaw(n, i))
          },
          location: r
        };
        break;
      }
      default:
        throw new Error("[DataValidationDropdownManagerService]: unknown type!");
    }
    if (h = this._cellDropdownManagerService.showDropdown({
      ...v,
      onHide: () => {
        this._activeDropdown = null, this._activeDropdown$.next(null);
      }
    }), !h)
      throw new Error("[DataValidationDropdownManagerService]: cannot show dropdown!");
    const p = new Tr();
    p.add(h), p.add({
      dispose: () => {
        var f, I;
        (I = (f = this._activeDropdown) == null ? void 0 : f.onHide) == null || I.call(f);
      }
    }), this._currentPopup = p;
  }
  hideDropdown() {
    this._activeDropdown && (this._currentPopup && this._currentPopup.dispose(), this._currentPopup = null, this._activeDropdown = null, this._activeDropdown$.next(null));
  }
  showDataValidationDropdown(t, r, n, i, o) {
    const a = this._univerInstanceService.getUnit(t, Y.UNIVER_SHEET);
    if (!a)
      return;
    const l = a.getSheetBySheetId(r);
    if (!l)
      return;
    const s = this._dataValidationModel.getRuleByLocation(a.getUnitId(), l.getSheetId(), n, i);
    if (!s)
      return;
    const d = this._dataValidatorRegistryService.getValidatorItem(s.type);
    if (!d || !d.dropdownType) {
      this.hideDropdown();
      return;
    }
    this.showDropdown({
      location: {
        workbook: a,
        worksheet: l,
        row: n,
        col: i,
        unitId: t,
        subUnitId: r
      },
      onHide: o
    });
  }
};
Ue = Li([
  he(0, de),
  he(1, M(be)),
  he(2, lr),
  he(3, M(ie)),
  he(4, M(zn)),
  he(5, M(fi)),
  he(6, M(ie)),
  he(7, Q),
  he(8, hr),
  he(9, M(ye)),
  he(10, an)
], Ue);
const Mt = "DataValidationPanel", Be = {
  id: "data-validation.operation.open-validation-panel",
  type: rt.OPERATION,
  handler(e, t) {
    if (!t)
      return !1;
    const { ruleId: r, isAdd: n } = t, i = e.get(Se), o = e.get(un), a = e.get(de), l = e.get(sr), s = Gn(a);
    if (!s) return !1;
    const { unitId: d, subUnitId: c } = s, h = r ? o.getRuleById(d, c, r) : void 0;
    i.open(), i.setActiveRule(h && {
      unitId: d,
      subUnitId: c,
      rule: h
    });
    const u = l.open({
      id: Mt,
      header: { title: n ? "dataValidation.panel.addTitle" : "dataValidation.panel.title" },
      children: { label: Mt },
      width: 312,
      onClose: () => i.close()
    });
    return i.setCloseDisposable(u), !0;
  }
}, mn = {
  id: "data-validation.operation.close-validation-panel",
  type: rt.OPERATION,
  handler(e) {
    return e.get(Se).close(), !0;
  }
}, fr = {
  id: "data-validation.operation.toggle-validation-panel",
  type: rt.OPERATION,
  handler(e) {
    const t = e.get(Q), r = e.get(Se);
    return r.open(), r.isOpen ? t.executeCommand(mn.id) : t.executeCommand(Be.id), !0;
  }
}, Lt = {
  type: rt.OPERATION,
  id: "sheet.operation.show-data-validation-dropdown",
  handler(e, t) {
    if (!t)
      return !1;
    const r = e.get(Ue), { unitId: n, subUnitId: i, row: o, column: a } = t, l = r.activeDropdown, s = l == null ? void 0 : l.location;
    return s && s.unitId === n && s.subUnitId === i && s.row === o && s.col === a || r.showDataValidationDropdown(
      n,
      i,
      o,
      a
    ), !0;
  }
}, mr = {
  type: rt.OPERATION,
  id: "sheet.operation.hide-data-validation-dropdown",
  handler(e, t) {
    return t ? (e.get(Ue).hideDropdown(), !0) : !1;
  }
}, kt = {
  type: rt.COMMAND,
  id: "data-validation.command.addRuleAndOpen",
  handler(e) {
    const t = e.get(de), r = Gn(t);
    if (!r) return !1;
    const { workbook: n, worksheet: i } = r, o = er(e), a = e.get(Q), l = n.getUnitId(), s = i.getSheetId(), d = {
      rule: o,
      unitId: l,
      subUnitId: s
    };
    return a.syncExecuteCommand(dn.id, d) ? (a.syncExecuteCommand(Be.id, {
      ruleId: o.uid,
      isAdd: !0
    }), !0) : !1;
  }
};
var Ni = Object.getOwnPropertyDescriptor, Ui = (e, t, r, n) => {
  for (var i = n > 1 ? void 0 : n ? Ni(t, r) : t, o = e.length - 1, a; o >= 0; o--)
    (a = e[o]) && (i = a(i) || i);
  return i;
}, He = (e, t) => (r, n) => t(r, n, e);
const Me = "SHEET_DATA_VALIDATION_ALERT";
let ht = class extends Re {
  constructor(e, t, r, n, i, o) {
    super(), this._hoverManagerService = e, this._cellAlertManagerService = t, this._univerInstanceService = r, this._localeService = n, this._zenZoneService = i, this._dataValidationModel = o, this._init();
  }
  _init() {
    this._initCellAlertPopup(), this._initZenService();
  }
  _initCellAlertPopup() {
    this.disposeWithMe(this._hoverManagerService.currentCell$.pipe(cr(100)).subscribe((e) => {
      var t;
      if (e) {
        const r = this._univerInstanceService.getUnit(e.location.unitId, Y.UNIVER_SHEET), n = r.getSheetBySheetId(e.location.subUnitId);
        if (!n) return;
        const i = this._dataValidationModel.getRuleByLocation(e.location.unitId, e.location.subUnitId, e.location.row, e.location.col);
        if (!i) {
          this._cellAlertManagerService.removeAlert(Me);
          return;
        }
        if (this._dataValidationModel.validator(i, { ...e.location, workbook: r, worksheet: n }) === Xe.INVALID) {
          const a = this._cellAlertManagerService.currentAlert.get(Me), l = (t = a == null ? void 0 : a.alert) == null ? void 0 : t.location;
          if (l && l.row === e.location.row && l.col === e.location.col && l.subUnitId === e.location.subUnitId && l.unitId === e.location.unitId) {
            this._cellAlertManagerService.removeAlert(Me);
            return;
          }
          const s = this._dataValidationModel.getValidator(i.type);
          if (!s) {
            this._cellAlertManagerService.removeAlert(Me);
            return;
          }
          this._cellAlertManagerService.showAlert({
            type: Si.ERROR,
            title: this._localeService.t("dataValidation.error.title"),
            message: s == null ? void 0 : s.getRuleFinalError(i, e.location),
            location: e.location,
            width: 200,
            height: 74,
            key: Me
          });
          return;
        }
      }
      this._cellAlertManagerService.removeAlert(Me);
    }));
  }
  _initZenService() {
    this.disposeWithMe(this._zenZoneService.visible$.subscribe((e) => {
      e && this._cellAlertManagerService.removeAlert(Me);
    }));
  }
};
ht = Ui([
  He(0, M(mi)),
  He(1, M(_i)),
  He(2, de),
  He(3, M(ce)),
  He(4, lr),
  He(5, M(ie))
], ht);
var Fi = Object.getOwnPropertyDescriptor, xi = (e, t, r, n) => {
  for (var i = n > 1 ? void 0 : n ? Fi(t, r) : t, o = e.length - 1, a; o >= 0; o--)
    (a = e[o]) && (i = a(i) || i);
  return i;
}, xt = (e, t) => (r, n) => t(r, n, e);
let Je = class extends Re {
  constructor(e, t, r) {
    super(), this._autoFillService = e, this._sheetDataValidationModel = t, this._injector = r, this._initAutoFill();
  }
  // eslint-disable-next-line max-lines-per-function
  _initAutoFill() {
    const e = () => ({ redos: [], undos: [] }), t = (n, i) => {
      const { source: o, target: a, unitId: l, subUnitId: s } = n, d = this._sheetDataValidationModel.getRuleObjectMatrix(l, s).clone(), c = en([o, a]), [h, u] = c.ranges, { mapFunc: v } = c, p = {
        row: h.startRow,
        col: h.startColumn
      }, b = wi(h, u), y = new It(), g = /* @__PURE__ */ new Set();
      b.forEach((_) => {
        const R = _.repeatStartCell, L = _.relativeRange, N = {
          startRow: p.row,
          startColumn: p.col,
          endColumn: p.col,
          endRow: p.row
        }, E = {
          startRow: R.row,
          startColumn: R.col,
          endColumn: R.col,
          endRow: R.row
        };
        Ar.foreach(L, (T, O) => {
          const P = Rt.getPositionRange(
            {
              startRow: T,
              startColumn: O,
              endColumn: O,
              endRow: T
            },
            N
          ), { row: k, col: W } = v(P.startRow, P.startColumn), X = this._sheetDataValidationModel.getRuleIdByLocation(l, s, k, W) || "", ee = Rt.getPositionRange(
            {
              startRow: T,
              startColumn: O,
              endColumn: O,
              endRow: T
            },
            E
          ), { row: te, col: oe } = v(ee.startRow, ee.startColumn);
          y.setValue(te, oe, X), g.add(X);
        });
      });
      const C = Array.from(g).map((_) => ({ id: _, ranges: Zt(y, (R) => R === _) }));
      d.addRangeRules(C);
      const f = d.diff(this._sheetDataValidationModel.getRules(l, s)), { redoMutations: I, undoMutations: w } = Jt(l, s, f, this._injector, "patched", i === ot.ONLY_FORMAT);
      return {
        undos: w,
        redos: I
      };
    }, r = {
      id: tr,
      onBeforeFillData: (n) => {
        const { source: i, unitId: o, subUnitId: a } = n;
        for (const l of i.rows)
          for (const s of i.cols) {
            const d = this._sheetDataValidationModel.getRuleByLocation(o, a, l, s);
            if (d && d.type === H.CHECKBOX) {
              this._autoFillService.setDisableApplyType(ot.SERIES, !0);
              return;
            }
          }
      },
      onFillData: (n, i, o) => o === ot.COPY || o === ot.ONLY_FORMAT || o === ot.SERIES ? t(n, o) : e(),
      onAfterFillData: () => {
      }
    };
    this.disposeWithMe(this._autoFillService.addHook(r));
  }
};
Je = xi([
  xt(0, Ci),
  xt(1, M(ie)),
  xt(2, M(ye))
], Je);
var Bi = Object.getOwnPropertyDescriptor, Wi = (e, t, r, n) => {
  for (var i = n > 1 ? void 0 : n ? Bi(t, r) : t, o = e.length - 1, a; o >= 0; o--)
    (a = e[o]) && (i = a(i) || i);
  return i;
}, Bt = (e, t) => (r, n) => t(r, n, e);
let Qe = class extends Re {
  constructor(t, r, n) {
    super();
    D(this, "_copyInfo");
    this._sheetClipboardService = t, this._sheetDataValidationModel = r, this._injector = n, this._initCopyPaste();
  }
  _initCopyPaste() {
    this._sheetClipboardService.addClipboardHook({
      id: tr,
      onBeforeCopy: (t, r, n) => this._collect(t, r, n),
      onPasteCells: (t, r, n, i) => {
        const { copyType: o = Tn.COPY, pasteType: a } = i, { range: l } = t || {}, { range: s, unitId: d, subUnitId: c } = r;
        return this._generateMutations(s, { copyType: o, pasteType: a, copyRange: l, unitId: d, subUnitId: c });
      }
    });
  }
  _collect(t, r, n) {
    const i = new It();
    this._copyInfo = {
      unitId: t,
      subUnitId: r,
      matrix: i
    };
    const o = this._injector.invoke((s) => Zr(n, s, t, r));
    if (!o)
      return;
    const { rows: a, cols: l } = o;
    a.forEach((s, d) => {
      l.forEach((c, h) => {
        const u = this._sheetDataValidationModel.getRuleIdByLocation(t, r, s, c);
        i.setValue(d, h, u != null ? u : "");
      });
    });
  }
  // eslint-disable-next-line max-lines-per-function
  _generateMutations(t, r) {
    if (!this._copyInfo)
      return { redos: [], undos: [] };
    if (r.copyType === Tn.CUT)
      return this._copyInfo = null, { redos: [], undos: [] };
    if (!this._copyInfo || !this._copyInfo.matrix.getSizeOf() || !r.copyRange)
      return { redos: [], undos: [] };
    if ([
      mt.SPECIAL_PASTE_COL_WIDTH,
      mt.SPECIAL_PASTE_VALUE,
      mt.SPECIAL_PASTE_FORMAT,
      mt.SPECIAL_PASTE_FORMULA
    ].includes(r.pasteType))
      return { redos: [], undos: [] };
    const { unitId: i, subUnitId: o } = this._copyInfo;
    if (r.unitId !== i || o !== r.subUnitId) {
      const a = this._sheetDataValidationModel.getRuleObjectMatrix(r.unitId, r.subUnitId).clone(), l = new It(), s = /* @__PURE__ */ new Set(), { ranges: [d, c], mapFunc: h } = en([r.copyRange, t]), u = Pn(d, c, !0), v = /* @__PURE__ */ new Map();
      u.forEach(({ startRange: g }) => {
        var C;
        (C = this._copyInfo) == null || C.matrix.forValue((f, I, w) => {
          const _ = Rt.getPositionRange(
            {
              startRow: f,
              endRow: f,
              startColumn: I,
              endColumn: I
            },
            g
          ), R = `${o}-${w}`, L = this._sheetDataValidationModel.getRuleById(i, o, w);
          !this._sheetDataValidationModel.getRuleById(r.unitId, r.subUnitId, R) && L && v.set(R, { ...L, uid: R });
          const { row: N, col: E } = h(_.startRow, _.startColumn);
          s.add(R), l.setValue(N, E, R);
        });
      });
      const p = Array.from(s).map((g) => ({ id: g, ranges: Zt(l, (C) => C === g) }));
      a.addRangeRules(p);
      const { redoMutations: b, undoMutations: y } = Jt(
        r.unitId,
        r.subUnitId,
        a.diffWithAddition(this._sheetDataValidationModel.getRules(r.unitId, r.subUnitId), v.values()),
        this._injector,
        "patched",
        !1
      );
      return {
        redos: b,
        undos: y
      };
    } else {
      const a = this._sheetDataValidationModel.getRuleObjectMatrix(i, o).clone(), l = new It(), s = /* @__PURE__ */ new Set(), { ranges: [d, c], mapFunc: h } = en([r.copyRange, t]);
      Pn(d, c, !0).forEach(({ startRange: y }) => {
        var g;
        (g = this._copyInfo) == null || g.matrix.forValue((C, f, I) => {
          const w = Rt.getPositionRange(
            {
              startRow: C,
              endRow: C,
              startColumn: f,
              endColumn: f
            },
            y
          ), { row: _, col: R } = h(w.startRow, w.startColumn);
          l.setValue(_, R, I), s.add(I);
        });
      });
      const v = Array.from(s).map((y) => ({ id: y, ranges: Zt(l, (g) => g === y) }));
      a.addRangeRules(v);
      const { redoMutations: p, undoMutations: b } = Jt(
        i,
        o,
        a.diff(this._sheetDataValidationModel.getRules(i, o)),
        this._injector,
        "patched",
        !1
      );
      return {
        redos: p,
        undos: b
      };
    }
  }
};
Qe = Wi([
  Bt(0, Ii),
  Bt(1, M(ie)),
  Bt(2, M(ye))
], Qe);
var $i = Object.getOwnPropertyDescriptor, Hi = (e, t, r, n) => {
  for (var i = n > 1 ? void 0 : n ? $i(t, r) : t, o = e.length - 1, a; o >= 0; o--)
    (a = e[o]) && (i = a(i) || i);
  return i;
}, Wt = (e, t) => (r, n) => t(r, n, e);
let et = class extends Re {
  constructor(e, t, r) {
    super(), this._localeService = e, this._commandService = t, this._sheetPermissionCheckController = r, this._commandExecutedListener();
  }
  _commandExecutedListener() {
    this.disposeWithMe(
      this._commandService.beforeCommandExecuted((e) => {
        e.id === dn.id && (this._sheetPermissionCheckController.permissionCheckWithRanges({
          workbookTypes: [qt],
          rangeTypes: [Gt],
          worksheetTypes: [Kt, zt]
        }) || this._sheetPermissionCheckController.blockExecuteWithoutPermission(this._localeService.t("permission.dialog.setStyleErr"))), e.id === nr.id && (this._sheetPermissionCheckController.permissionCheckWithRanges({
          workbookTypes: [qt],
          rangeTypes: [Gt],
          worksheetTypes: [Kt, zt]
        }, e.params.ranges) || this._sheetPermissionCheckController.blockExecuteWithoutPermission(this._localeService.t("permission.dialog.setStyleErr")));
      })
    );
  }
};
et = Hi([
  Wt(0, M(ce)),
  Wt(1, Q),
  Wt(2, M(Kr))
], et);
const _r = "sheet.menu.data-validation";
function ji(e) {
  return {
    id: _r,
    type: hn.SUBITEMS,
    icon: "DataValidationIcon",
    tooltip: "dataValidation.title",
    hidden$: di(e, Y.UNIVER_SHEET),
    disabled$: Ri(e, { workbookTypes: [qt], worksheetTypes: [zt, Kt], rangeTypes: [Gt] })
  };
}
function Yi(e) {
  return {
    id: Be.id,
    title: "dataValidation.panel.title",
    type: hn.BUTTON
  };
}
function Xi(e) {
  return {
    id: kt.id,
    title: "dataValidation.panel.add",
    type: hn.BUTTON
  };
}
const Zi = {
  [ci.RULES]: {
    [_r]: {
      order: 0,
      menuItemFactory: ji,
      [Be.id]: {
        order: 0,
        menuItemFactory: Yi
      },
      [kt.id]: {
        order: 1,
        menuItemFactory: Xi
      }
    }
  }
};
var Ki = Object.getOwnPropertyDescriptor, Sr = (e, t, r, n) => {
  for (var i = n > 1 ? void 0 : n ? Ki(t, r) : t, o = e.length - 1, a; o >= 0; o--)
    (a = e[o]) && (i = a(i) || i);
  return i;
}, Z = (e, t) => (r, n) => t(r, n, e);
const Cr = {
  tr: {
    size: 6,
    color: "#fe4b4b"
  }
};
let tt = class extends sn {
  constructor(e, t, r, n, i, o, a, l, s, d, c) {
    super(), this._commandService = e, this._menuManagerService = t, this._renderManagerService = r, this._univerInstanceService = n, this._autoHeightController = i, this._dropdownManagerService = o, this._sheetDataValidationModel = a, this._dataValidatorRegistryService = l, this._sheetInterceptorService = s, this._dataValidationCacheService = d, this._editorBridgeService = c, this._initMenu(), this._initDropdown(), this._initViewModelIntercept(), this._initAutoHeight();
  }
  _initMenu() {
    this._menuManagerService.mergeMenu(Zi);
  }
  _initDropdown() {
    this._editorBridgeService && this.disposeWithMe(this._editorBridgeService.visible$.subscribe((e) => {
      var r;
      if (!e.visible) {
        ((r = this._dropdownManagerService.activeDropdown) == null ? void 0 : r.trigger) === "editor-bridge" && this._dropdownManagerService.hideDropdown();
        return;
      }
      const t = this._editorBridgeService.getEditCellState();
      if (t) {
        const { unitId: n, sheetId: i, row: o, column: a } = t, l = this._univerInstanceService.getUniverSheetInstance(n);
        if (!l)
          return;
        const s = this._sheetDataValidationModel.getRuleByLocation(n, i, o, a);
        if (!s)
          return;
        const d = this._dataValidatorRegistryService.getValidatorItem(s.type);
        if (!(d != null && d.dropdownType))
          return;
        const c = l.getActiveSheet();
        if (!c) return;
        const h = this._dropdownManagerService.activeDropdown, u = h == null ? void 0 : h.location;
        if (u && u.unitId === n && u.subUnitId === i && u.row === o && u.col === a)
          return;
        this._dropdownManagerService.showDropdown(
          {
            location: {
              unitId: n,
              subUnitId: i,
              row: o,
              col: a,
              workbook: l,
              worksheet: c
            },
            trigger: "editor-bridge",
            closeOnOutSide: !1
          }
        );
      }
    }));
  }
  // eslint-disable-next-line max-lines-per-function
  _initViewModelIntercept() {
    this.disposeWithMe(
      this._sheetInterceptorService.intercept(
        qn.CELL_CONTENT,
        {
          effect: Hn.Style,
          // must be after numfmt
          priority: Jn.DATA_VALIDATION,
          // eslint-disable-next-line max-lines-per-function, complexity
          handler: (e, t, r) => {
            var g, C, f, I, w;
            const { row: n, col: i, unitId: o, subUnitId: a, workbook: l, worksheet: s } = t, d = this._sheetDataValidationModel.getRuleIdByLocation(o, a, n, i);
            if (!d)
              return r(e);
            const c = this._sheetDataValidationModel.getRuleById(o, a, d);
            if (!c)
              return r(e);
            const h = (g = this._dataValidationCacheService.getValue(o, a, n, i)) != null ? g : Xe.VALID, u = this._dataValidatorRegistryService.getValidatorItem(c.type), v = t.rawData;
            let p;
            const b = {
              get value() {
                var _;
                return p !== void 0 || (p = (_ = re(v)) != null ? _ : null), p;
              }
            }, y = {
              get value() {
                var _;
                return `${(_ = b.value) != null ? _ : ""}`;
              }
            };
            return (!e || e === t.rawData) && (e = { ...t.rawData }), e.markers = {
              ...e == null ? void 0 : e.markers,
              ...h === Xe.INVALID ? Cr : null
            }, e.customRender = [
              ...(C = e == null ? void 0 : e.customRender) != null ? C : [],
              ...u != null && u.canvasRender ? [u.canvasRender] : []
            ], e.fontRenderExtension = {
              ...e == null ? void 0 : e.fontRenderExtension,
              isSkip: ((f = e == null ? void 0 : e.fontRenderExtension) == null ? void 0 : f.isSkip) || ((I = u == null ? void 0 : u.skipDefaultFontRender) == null ? void 0 : I.call(u, c, b.value, t))
            }, e.interceptorStyle = {
              ...e == null ? void 0 : e.interceptorStyle,
              ...u == null ? void 0 : u.getExtraStyle(c, y.value, {
                get style() {
                  const _ = l.getStyles();
                  return (typeof (e == null ? void 0 : e.s) == "string" ? _.get(e == null ? void 0 : e.s) : e == null ? void 0 : e.s) || {};
                }
              }, n, i)
            }, e.interceptorAutoHeight = () => {
              var N, E, T, O, P, k;
              const _ = (E = (N = this._renderManagerService.getRenderById(o)) == null ? void 0 : N.with(dt).getSkeletonParam(a)) == null ? void 0 : E.skeleton;
              if (!_)
                return;
              const R = _.worksheet.getMergedCell(n, i), L = {
                data: e,
                style: _.getStyles().getStyleByCell(e),
                primaryWithCoord: _.getCellWithCoordByIndex((T = R == null ? void 0 : R.startRow) != null ? T : n, (O = R == null ? void 0 : R.startColumn) != null ? O : i),
                unitId: o,
                subUnitId: a,
                row: n,
                col: i,
                workbook: l,
                worksheet: s
              };
              return (k = (P = u == null ? void 0 : u.canvasRender) == null ? void 0 : P.calcCellAutoHeight) == null ? void 0 : k.call(P, L);
            }, e.interceptorAutoWidth = () => {
              var N, E, T, O, P, k;
              const _ = (E = (N = this._renderManagerService.getRenderById(o)) == null ? void 0 : N.with(dt).getSkeletonParam(a)) == null ? void 0 : E.skeleton;
              if (!_)
                return;
              const R = _.worksheet.getMergedCell(n, i), L = {
                data: e,
                style: _.getStyles().getStyleByCell(e),
                primaryWithCoord: _.getCellWithCoordByIndex((T = R == null ? void 0 : R.startRow) != null ? T : n, (O = R == null ? void 0 : R.startColumn) != null ? O : i),
                unitId: o,
                subUnitId: a,
                row: n,
                col: i,
                workbook: l,
                worksheet: s
              };
              return (k = (P = u == null ? void 0 : u.canvasRender) == null ? void 0 : P.calcCellAutoWidth) == null ? void 0 : k.call(P, L);
            }, e.coverable = ((w = e == null ? void 0 : e.coverable) != null ? w : !0) && !(c.type === H.LIST || c.type === H.LIST_MULTIPLE), r(e);
          }
        }
      )
    );
  }
  _initAutoHeight() {
    this._sheetDataValidationModel.ruleChange$.pipe(
      // patched data-validation change don't need to re-calc row height
      // re-calc of row height will be triggered precisely by the origin command
      gn((e) => e.source === "command"),
      ur(100)
    ).subscribe((e) => {
      if (e.length === 0)
        return;
      const t = [];
      if (e.forEach((r) => {
        var n;
        (r.rule.type === H.LIST_MULTIPLE || r.rule.type === H.LIST) && (n = r.rule) != null && n.ranges && t.push(...r.rule.ranges);
      }), t.length) {
        const r = this._autoHeightController.getUndoRedoParamsOfAutoHeight(t);
        jn(r.redos, this._commandService);
      }
    });
  }
};
tt = Sr([
  Z(0, Q),
  Z(1, ui),
  Z(2, Fe),
  Z(3, de),
  Z(4, M(pr)),
  Z(5, M(Ue)),
  Z(6, M(ie)),
  Z(7, M(be)),
  Z(8, M(ln)),
  Z(9, M(rr)),
  Z(10, Lr(hr))
], tt);
let kn = class extends sn {
  constructor(e, t, r, n, i, o, a) {
    super(), this._commandService = e, this._renderManagerService = t, this._autoHeightController = r, this._dataValidatorRegistryService = n, this._sheetInterceptorService = i, this._sheetDataValidationModel = o, this._dataValidationCacheService = a, this._initViewModelIntercept(), this._initAutoHeight();
  }
  // eslint-disable-next-line max-lines-per-function
  _initViewModelIntercept() {
    this.disposeWithMe(
      this._sheetInterceptorService.intercept(
        qn.CELL_CONTENT,
        {
          effect: Hn.Style,
          // must be after numfmt
          priority: Jn.DATA_VALIDATION,
          // eslint-disable-next-line complexity, max-lines-per-function
          handler: (e, t, r) => {
            var y, g, C, f, I;
            const { row: n, col: i, unitId: o, subUnitId: a, workbook: l, worksheet: s } = t, d = this._sheetDataValidationModel.getRuleIdByLocation(o, a, n, i);
            if (!d)
              return r(e);
            const c = this._sheetDataValidationModel.getRuleById(o, a, d);
            if (!c)
              return r(e);
            const h = (y = this._dataValidationCacheService.getValue(o, a, n, i)) != null ? y : Xe.VALID, u = this._dataValidatorRegistryService.getValidatorItem(c.type), v = s.getCellRaw(n, i), p = re(v), b = `${p != null ? p : ""}`;
            return (!e || e === t.rawData) && (e = { ...t.rawData }), e.markers = {
              ...e == null ? void 0 : e.markers,
              ...h === Xe.INVALID ? Cr : null
            }, e.customRender = [
              ...(g = e == null ? void 0 : e.customRender) != null ? g : [],
              ...u != null && u.canvasRender ? [u.canvasRender] : []
            ], e.fontRenderExtension = {
              ...e == null ? void 0 : e.fontRenderExtension,
              isSkip: ((C = e == null ? void 0 : e.fontRenderExtension) == null ? void 0 : C.isSkip) || ((f = u == null ? void 0 : u.skipDefaultFontRender) == null ? void 0 : f.call(u, c, p, t))
            }, e.interceptorStyle = {
              ...e == null ? void 0 : e.interceptorStyle,
              ...u == null ? void 0 : u.getExtraStyle(c, b, {
                get style() {
                  const w = l.getStyles();
                  return (typeof (e == null ? void 0 : e.s) == "string" ? w.get(e == null ? void 0 : e.s) : e == null ? void 0 : e.s) || {};
                }
              }, n, i)
            }, e.interceptorAutoHeight = () => {
              var L, N, E, T, O, P;
              const w = (N = (L = this._renderManagerService.getRenderById(o)) == null ? void 0 : L.with(dt).getSkeletonParam(a)) == null ? void 0 : N.skeleton;
              if (!w)
                return;
              const _ = w.worksheet.getMergedCell(n, i), R = {
                data: e,
                style: w.getStyles().getStyleByCell(e),
                primaryWithCoord: w.getCellWithCoordByIndex((E = _ == null ? void 0 : _.startRow) != null ? E : n, (T = _ == null ? void 0 : _.startColumn) != null ? T : i),
                unitId: o,
                subUnitId: a,
                row: n,
                col: i,
                workbook: l,
                worksheet: s
              };
              return (P = (O = u == null ? void 0 : u.canvasRender) == null ? void 0 : O.calcCellAutoHeight) == null ? void 0 : P.call(O, R);
            }, e.interceptorAutoWidth = () => {
              var L, N, E, T, O, P;
              const w = (N = (L = this._renderManagerService.getRenderById(o)) == null ? void 0 : L.with(dt).getSkeletonParam(a)) == null ? void 0 : N.skeleton;
              if (!w)
                return;
              const _ = w.worksheet.getMergedCell(n, i), R = {
                data: e,
                style: w.getStyles().getStyleByCell(e),
                primaryWithCoord: w.getCellWithCoordByIndex((E = _ == null ? void 0 : _.startRow) != null ? E : n, (T = _ == null ? void 0 : _.startColumn) != null ? T : i),
                unitId: o,
                subUnitId: a,
                row: n,
                col: i,
                workbook: l,
                worksheet: s
              };
              return (P = (O = u == null ? void 0 : u.canvasRender) == null ? void 0 : O.calcCellAutoWidth) == null ? void 0 : P.call(O, R);
            }, e.coverable = ((I = e == null ? void 0 : e.coverable) != null ? I : !0) && !(c.type === H.LIST || c.type === H.LIST_MULTIPLE), r(e);
          }
        }
      )
    );
  }
  _initAutoHeight() {
    this._sheetDataValidationModel.ruleChange$.pipe(
      gn((e) => e.source === "command"),
      ur(16)
    ).subscribe((e) => {
      const t = [];
      if (e.forEach((r) => {
        var n;
        (r.rule.type === H.LIST_MULTIPLE || r.rule.type === H.LIST) && (n = r.rule) != null && n.ranges && t.push(...r.rule.ranges);
      }), t.length) {
        const r = this._autoHeightController.getUndoRedoParamsOfAutoHeight(t);
        jn(r.redos, this._commandService);
      }
    });
  }
};
kn = Sr([
  Z(0, Q),
  Z(1, Fe),
  Z(2, M(pr)),
  Z(3, M(be)),
  Z(4, M(ln)),
  Z(5, M(ie)),
  Z(6, M(rr))
], kn);
var zi = Object.getOwnPropertyDescriptor, Gi = (e, t, r, n) => {
  for (var i = n > 1 ? void 0 : n ? zi(t, r) : t, o = e.length - 1, a; o >= 0; o--)
    (a = e[o]) && (i = a(i) || i);
  return i;
}, Nn = (e, t) => (r, n) => t(r, n, e);
let Et = class extends Re {
  constructor(e, t, r) {
    super(), this._context = e, this._sheetDataValidationModel = t, this._sheetSkeletonManagerService = r, this._initSkeletonChange();
  }
  _initSkeletonChange() {
    const e = (t) => {
      var n;
      if (!t.length)
        return;
      const r = /* @__PURE__ */ new Set();
      t.forEach((i) => {
        r.add(i.subUnitId);
      }), r.forEach((i) => {
        var o;
        (o = this._sheetSkeletonManagerService.getSkeletonParam(i)) == null || o.skeleton.makeDirty(!0);
      }), (n = this._context.mainComponent) == null || n.makeForceDirty();
    };
    this.disposeWithMe(this._sheetDataValidationModel.validStatusChange$.pipe(kr(16)).subscribe(e));
  }
};
Et = Gi([
  Nn(1, M(ie)),
  Nn(2, M(dt))
], Et);
var le = function() {
  return le = Object.assign || function(e) {
    for (var t, r = 1, n = arguments.length; r < n; r++) {
      t = arguments[r];
      for (var i in t) Object.prototype.hasOwnProperty.call(t, i) && (e[i] = t[i]);
    }
    return e;
  }, le.apply(this, arguments);
}, qi = function(e, t) {
  var r = {};
  for (var n in e) Object.prototype.hasOwnProperty.call(e, n) && t.indexOf(n) < 0 && (r[n] = e[n]);
  if (e != null && typeof Object.getOwnPropertySymbols == "function")
    for (var i = 0, n = Object.getOwnPropertySymbols(e); i < n.length; i++)
      t.indexOf(n[i]) < 0 && Object.prototype.propertyIsEnumerable.call(e, n[i]) && (r[n[i]] = e[n[i]]);
  return r;
}, We = xe(function(e, t) {
  var r = e.icon, n = e.id, i = e.className, o = e.extend, a = qi(e, ["icon", "id", "className", "extend"]), l = "univerjs-icon univerjs-icon-".concat(n, " ").concat(i || "").trim(), s = pt("_".concat(eo()));
  return wr(r, "".concat(n), { defIds: r.defIds, idSuffix: s.current }, le({ ref: t, className: l }, a), o);
});
function wr(e, t, r, n, i) {
  return Ie(e.tag, le(le({ key: t }, Ji(e, r, i)), n), (Qi(e, r).children || []).map(function(o, a) {
    return wr(o, "".concat(t, "-").concat(e.tag, "-").concat(a), r, void 0, i);
  }));
}
function Ji(e, t, r) {
  var n = le({}, e.attrs);
  r != null && r.colorChannel1 && n.fill === "colorChannel1" && (n.fill = r.colorChannel1), e.tag === "mask" && n.id && (n.id = n.id + t.idSuffix), Object.entries(n).forEach(function(o) {
    var a = o[0], l = o[1];
    a === "mask" && typeof l == "string" && (n[a] = l.replace(/url\(#(.*)\)/, "url(#$1".concat(t.idSuffix, ")")));
  });
  var i = t.defIds;
  return !i || i.length === 0 || (e.tag === "use" && n["xlink:href"] && (n["xlink:href"] = n["xlink:href"] + t.idSuffix), Object.entries(n).forEach(function(o) {
    var a = o[0], l = o[1];
    typeof l == "string" && (n[a] = l.replace(/url\(#(.*)\)/, "url(#$1".concat(t.idSuffix, ")")));
  })), n;
}
function Qi(e, t) {
  var r, n = t.defIds;
  return !n || n.length === 0 ? e : e.tag === "defs" && (!((r = e.children) === null || r === void 0) && r.length) ? le(le({}, e), { children: e.children.map(function(i) {
    return typeof i.attrs.id == "string" && n && n.includes(i.attrs.id) ? le(le({}, i), { attrs: le(le({}, i.attrs), { id: i.attrs.id + t.idSuffix }) }) : i;
  }) }) : e;
}
function eo() {
  return Math.random().toString(36).substring(2, 8);
}
We.displayName = "UniverIcon";
var to = { tag: "svg", attrs: { xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 17 16", width: "1em", height: "1em" }, children: [{ tag: "path", attrs: { fill: "currentColor", d: "M1.4917 3.07803C1.4917 2.19437 2.20804 1.47803 3.0917 1.47803H5.6917C6.57536 1.47803 7.2917 2.19437 7.2917 3.07803V5.67803C7.2917 6.56168 6.57535 7.27803 5.6917 7.27803H3.0917C2.20804 7.27803 1.4917 6.56168 1.4917 5.67803V3.07803ZM3.0917 2.67803C2.87078 2.67803 2.6917 2.85711 2.6917 3.07803V5.67803C2.6917 5.89894 2.87079 6.07803 3.0917 6.07803H5.6917C5.91261 6.07803 6.0917 5.89894 6.0917 5.67803V3.07803C6.0917 2.85711 5.91261 2.67803 5.6917 2.67803H3.0917Z", fillRule: "evenodd", clipRule: "evenodd" } }, { tag: "path", attrs: { fill: "currentColor", d: "M14.6175 2.45279C14.8518 2.68711 14.8518 3.06701 14.6175 3.30132L11.6151 6.30365C11.3957 6.52307 11.0451 6.53897 10.8067 6.34031L8.80915 4.67566C8.55458 4.46352 8.52019 4.08518 8.73233 3.83062C8.94447 3.57605 9.32281 3.54166 9.57737 3.7538L11.154 5.06767L13.769 2.45278C14.0033 2.21847 14.3832 2.21848 14.6175 2.45279Z" } }, { tag: "path", attrs: { fill: "currentColor", d: "M14.1175 9.19746C14.3518 9.43178 14.3518 9.81168 14.1175 10.046L12.5418 11.6217L14.1175 13.1975C14.3518 13.4318 14.3518 13.8117 14.1175 14.046C13.8832 14.2803 13.5033 14.2803 13.269 14.046L11.6933 12.4703L10.1175 14.046C9.88321 14.2803 9.50331 14.2803 9.269 14.046C9.03468 13.8117 9.03468 13.4318 9.269 13.1975L10.8447 11.6217L9.269 10.046C9.03468 9.81168 9.03468 9.43178 9.269 9.19746C9.50331 8.96315 9.88321 8.96315 10.1175 9.19746L11.6933 10.7732L13.269 9.19746C13.5033 8.96315 13.8832 8.96315 14.1175 9.19746Z" } }, { tag: "path", attrs: { fill: "currentColor", d: "M3.0917 8.72168C2.20804 8.72168 1.4917 9.43802 1.4917 10.3217V12.9217C1.4917 13.8053 2.20804 14.5217 3.0917 14.5217H5.6917C6.57535 14.5217 7.2917 13.8053 7.2917 12.9217V10.3217C7.2917 9.43802 6.57536 8.72168 5.6917 8.72168H3.0917ZM2.6917 10.3217C2.6917 10.1008 2.87078 9.92168 3.0917 9.92168H5.6917C5.91261 9.92168 6.0917 10.1008 6.0917 10.3217V12.9217C6.0917 13.1426 5.91261 13.3217 5.6917 13.3217H3.0917C2.87079 13.3217 2.6917 13.1426 2.6917 12.9217V10.3217Z", fillRule: "evenodd", clipRule: "evenodd" } }] }, Ir = xe(function(e, t) {
  return Ie(We, Object.assign({}, e, {
    id: "data-validation-icon",
    ref: t,
    icon: to
  }));
});
Ir.displayName = "DataValidationIcon";
var no = { tag: "svg", attrs: { xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 16 16", width: "1em", height: "1em" }, children: [{ tag: "path", attrs: { fill: "currentColor", d: "M5.3313 1.4667C5.3313 1.13533 5.59993 0.866699 5.9313 0.866699H10.069C10.4004 0.866699 10.669 1.13533 10.669 1.4667C10.669 1.79807 10.4004 2.0667 10.069 2.0667H5.9313C5.59993 2.0667 5.3313 1.79807 5.3313 1.4667Z" } }, { tag: "path", attrs: { fill: "currentColor", d: "M1.09985 3.64443C1.09985 3.31306 1.36848 3.04443 1.69985 3.04443H14.2999C14.6312 3.04443 14.8999 3.31306 14.8999 3.64443C14.8999 3.9758 14.6312 4.24443 14.2999 4.24443H1.69985C1.36848 4.24443 1.09985 3.9758 1.09985 3.64443Z" } }, { tag: "path", attrs: { fill: "currentColor", d: "M6.12398 8.30171C6.35829 8.0674 6.73819 8.0674 6.97251 8.30171L8.00007 9.32928L9.02764 8.30171C9.26195 8.0674 9.64185 8.0674 9.87617 8.30171C10.1105 8.53603 10.1105 8.91593 9.87617 9.15024L8.8486 10.1778L9.87617 11.2054C10.1105 11.4397 10.1105 11.8196 9.87617 12.0539C9.64185 12.2882 9.26195 12.2882 9.02764 12.0539L8.00007 11.0263L6.97251 12.0539C6.73819 12.2882 6.35829 12.2882 6.12398 12.0539C5.88966 11.8196 5.88966 11.4397 6.12398 11.2054L7.15154 10.1778L6.12398 9.15024C5.88966 8.91593 5.88966 8.53603 6.12398 8.30171Z" } }, { tag: "path", attrs: { fill: "currentColor", d: "M4.75332 5.22217C3.86966 5.22217 3.15332 5.93851 3.15332 6.82217V12.5331C3.15332 13.9691 4.31738 15.1332 5.75332 15.1332H10.2465C11.6825 15.1332 12.8465 13.9691 12.8465 12.5331V6.82217C12.8465 5.93851 12.1302 5.22217 11.2465 5.22217H4.75332ZM4.35332 6.82217C4.35332 6.60125 4.53241 6.42217 4.75332 6.42217H11.2465C11.4674 6.42217 11.6465 6.60125 11.6465 6.82217V12.5331C11.6465 13.3063 11.0197 13.9332 10.2465 13.9332H5.75332C4.98012 13.9332 4.35332 13.3063 4.35332 12.5331V6.82217Z", fillRule: "evenodd", clipRule: "evenodd" } }] }, _n = xe(function(e, t) {
  return Ie(We, Object.assign({}, e, {
    id: "delete-icon",
    ref: t,
    icon: no
  }));
});
_n.displayName = "DeleteIcon";
var ro = { tag: "svg", attrs: { xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 16 16", width: "1em", height: "1em" }, children: [{ tag: "path", attrs: { fill: "currentColor", d: "M8.6 1.99991C8.60001 1.66854 8.33138 1.39991 8.00001 1.3999C7.66864 1.3999 7.40001 1.66853 7.4 1.9999L7.39996 7.3999H1.9999C1.66853 7.3999 1.3999 7.66853 1.3999 7.9999C1.3999 8.33127 1.66853 8.5999 1.9999 8.5999H7.39995L7.3999 13.9999C7.3999 14.3313 7.66853 14.5999 7.9999 14.5999C8.33127 14.5999 8.5999 14.3313 8.5999 13.9999L8.59995 8.5999H13.9999C14.3313 8.5999 14.5999 8.33127 14.5999 7.9999C14.5999 7.66853 14.3313 7.3999 13.9999 7.3999H8.59996L8.6 1.99991Z" } }] }, Rr = xe(function(e, t) {
  return Ie(We, Object.assign({}, e, {
    id: "increase-icon",
    ref: t,
    icon: ro
  }));
});
Rr.displayName = "IncreaseIcon";
var io = { tag: "svg", attrs: { xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 16 16", width: "1em", height: "1em" }, children: [{ tag: "path", attrs: { fill: "currentColor", d: "M11.3536 6.14645C11.5488 6.34171 11.5488 6.65829 11.3536 6.85355L8.35355 9.85355C8.15829 10.0488 7.84171 10.0488 7.64645 9.85355L4.64645 6.85355C4.45118 6.65829 4.45118 6.34171 4.64645 6.14645C4.84171 5.95118 5.15829 5.95118 5.35355 6.14645L8 8.79289L10.6464 6.14645C10.8417 5.95118 11.1583 5.95118 11.3536 6.14645Z", fillRule: "evenodd", clipRule: "evenodd" } }] }, Sn = xe(function(e, t) {
  return Ie(We, Object.assign({}, e, {
    id: "more-down-icon",
    ref: t,
    icon: io
  }));
});
Sn.displayName = "MoreDownIcon";
var oo = { tag: "svg", attrs: { xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 16 16", width: "1em", height: "1em" }, children: [{ tag: "path", attrs: { fill: "currentColor", d: "M4.64645 9.85355C4.45118 9.65829 4.45118 9.34171 4.64645 9.14645L7.64645 6.14645C7.84171 5.95118 8.15829 5.95118 8.35355 6.14645L11.3536 9.14645C11.5488 9.34171 11.5488 9.65829 11.3536 9.85355C11.1583 10.0488 10.8417 10.0488 10.6464 9.85355L8 7.20711L5.35355 9.85355C5.15829 10.0488 4.84171 10.0488 4.64645 9.85355Z", fillRule: "evenodd", clipRule: "evenodd" } }] }, yr = xe(function(e, t) {
  return Ie(We, Object.assign({}, e, {
    id: "more-up-icon",
    ref: t,
    icon: oo
  }));
});
yr.displayName = "MoreUpIcon";
var ao = { tag: "svg", attrs: { xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 16 16", width: "1em", height: "1em" }, children: [{ tag: "mask", attrs: { id: "mask0_622_8", width: 16, height: 16, x: 0, y: 0, maskUnits: "userSpaceOnUse" }, children: [{ tag: "path", attrs: { fill: "#D9D9D9", d: "M0 0H16V16H0z" } }] }, { tag: "g", attrs: { fill: "currentColor", mask: "url(#mask0_622_8)" }, children: [{ tag: "path", attrs: { d: "M6 5C6.55228 5 7 4.55228 7 4C7 3.44772 6.55228 3 6 3C5.44772 3 5 3.44772 5 4C5 4.55228 5.44772 5 6 5Z" } }, { tag: "path", attrs: { d: "M6 9C6.55228 9 7 8.55229 7 8C7 7.44772 6.55228 7 6 7C5.44772 7 5 7.44772 5 8C5 8.55229 5.44772 9 6 9Z" } }, { tag: "path", attrs: { d: "M7 12C7 12.5523 6.55228 13 6 13C5.44772 13 5 12.5523 5 12C5 11.4477 5.44772 11 6 11C6.55228 11 7 11.4477 7 12Z" } }, { tag: "path", attrs: { d: "M10 5C10.5523 5 11 4.55228 11 4C11 3.44772 10.5523 3 10 3C9.44771 3 9 3.44772 9 4C9 4.55228 9.44771 5 10 5Z" } }, { tag: "path", attrs: { d: "M11 8C11 8.55229 10.5523 9 10 9C9.44771 9 9 8.55229 9 8C9 7.44772 9.44771 7 10 7C10.5523 7 11 7.44772 11 8Z" } }, { tag: "path", attrs: { d: "M10 13C10.5523 13 11 12.5523 11 12C11 11.4477 10.5523 11 10 11C9.44771 11 9 11.4477 9 12C9 12.5523 9.44771 13 10 13Z" } }] }] }, br = xe(function(e, t) {
  return Ie(We, Object.assign({}, e, {
    id: "sequence-icon",
    ref: t,
    icon: ao
  }));
});
br.displayName = "SequenceIcon";
function so(e) {
  var d;
  const t = F(ce), r = F(pn), { value: n, onChange: i, extraComponent: o } = e, [a, l] = B(!1), s = o ? r.get(o) : null;
  return /* @__PURE__ */ $(ut, { children: [
    /* @__PURE__ */ $(
      "div",
      {
        className: "univer-mb-3 univer-flex univer-cursor-pointer univer-items-center univer-text-sm univer-text-gray-900 dark:!univer-text-white",
        onClick: () => l(!a),
        children: [
          t.t("dataValidation.panel.options"),
          a ? /* @__PURE__ */ S(yr, { className: "univer-ml-1" }) : /* @__PURE__ */ S(Sn, { className: "univer-ml-1" })
        ]
      }
    ),
    a && /* @__PURE__ */ $(ut, { children: [
      s ? /* @__PURE__ */ S(s, { value: n, onChange: i }) : null,
      /* @__PURE__ */ S(
        j,
        {
          label: t.t("dataValidation.panel.invalid"),
          children: /* @__PURE__ */ $(
            vn,
            {
              value: `${(d = n.errorStyle) != null ? d : lt.WARNING}`,
              onChange: (c) => i({ ...n, errorStyle: +c }),
              children: [
                /* @__PURE__ */ S(Ae, { value: `${lt.WARNING}`, children: t.t("dataValidation.panel.showWarning") }),
                /* @__PURE__ */ S(Ae, { value: `${lt.STOP}`, children: t.t("dataValidation.panel.rejectInput") })
              ]
            }
          )
        }
      ),
      /* @__PURE__ */ S(
        j,
        {
          label: t.t("dataValidation.panel.messageInfo"),
          children: /* @__PURE__ */ S(
            At,
            {
              checked: n.showErrorMessage,
              onChange: () => i({
                ...n,
                showErrorMessage: !n.showErrorMessage
              }),
              children: t.t("dataValidation.panel.showInfo")
            }
          )
        }
      ),
      n.showErrorMessage ? /* @__PURE__ */ S(j, { children: /* @__PURE__ */ S(Le, { value: n.error, onChange: (c) => i({ ...n, error: c }) }) }) : null
    ] })
  ] });
}
const lo = (e) => Nr(
  async (t, r, n, i) => {
    const o = await e.executeCommand(t, r, n);
    i == null || i(o);
  },
  1e3
);
function co(e, t, r) {
  var n, i, o, a;
  return t ? ((i = (n = e.getUnit(t)) == null ? void 0 : n.getSheetBySheetName(r)) == null ? void 0 : i.getSheetId()) || "" : ((a = (o = e.getCurrentUnitForType(Y.UNIVER_SHEET)) == null ? void 0 : o.getSheetBySheetName(r)) == null ? void 0 : a.getSheetId()) || "";
}
function uo() {
  var V, U;
  const [e, t] = B(0), r = F(Se), n = Pe(r.activeRule$, r.activeRule), { unitId: i, subUnitId: o, rule: a } = n || {}, l = a.uid, s = F(be), d = F(de), c = F(pn), h = F(Q), u = F(un), v = F(ce), [p, b] = B(a), y = s.getValidatorItem(p.type), [g, C] = B(!1), f = s.getValidatorsByScope(ai.SHEET), [I, w] = B(() => p.ranges.map((m) => ({ unitId: "", sheetId: "", range: m }))), _ = ct(() => lo(h), [h]), [R, L] = B(!1), [N, E] = B(!1), T = pt(null), O = F(zn);
  if (ke(() => () => {
    const m = O.getCurrentLastSelection();
    m && O.setSelections([m]);
  }, [O]), ke(() => {
    h.onCommandExecuted((m) => {
      (m.id === Ur.id || m.id === Fr.id) && setTimeout(() => {
        const A = u.getRuleById(i, o, l);
        t((x) => x + 1), A && (b(A), w(A.ranges.map((x) => ({ unitId: "", sheetId: "", range: x }))));
      }, 20);
    });
  }, [h, u, l, o, i]), !y)
    return null;
  const P = y.operators, k = y.operatorNames, W = p.operator ? si.includes(p.operator) : !1, X = () => {
    var m, A, x;
    (A = (m = T.current) == null ? void 0 : m.editor) != null && A.isFocus() && ee((x = T.current) == null ? void 0 : x.getValue()), !(!p.ranges.length || R) && (y.validatorFormula(p, i, o).success ? r.setActiveRule(null) : C(!0));
  }, ee = Qt((m) => {
    const A = m.split(",").filter(Boolean).map(Ei).map((J) => {
      const Cn = J.sheetName;
      if (Cn) {
        const Er = co(d, J.unitId, Cn);
        return { ...J, sheetId: Er };
      }
      return {
        ...J,
        sheetId: ""
      };
    });
    if (xr(A, I))
      return;
    w(A);
    const x = A.filter((J) => (!J.unitId || J.unitId === i) && (!J.sheetId || J.sheetId === o)).map((J) => J.range);
    if (b({
      ...p,
      ranges: x
    }), x.length === 0)
      return;
    const we = {
      unitId: i,
      subUnitId: o,
      ruleId: l,
      ranges: x
    };
    _(nr.id, we);
  }), te = (m) => {
    if (In(m, Dn(p)))
      return;
    b({
      ...p,
      ...m
    });
    const A = {
      unitId: i,
      subUnitId: o,
      ruleId: l,
      setting: m
    };
    _(
      Mn.id,
      A,
      void 0
    );
  }, oe = async () => {
    await h.executeCommand(ir.id, {
      ruleId: l,
      unitId: i,
      subUnitId: o
    }), r.setActiveRule(null);
  }, ue = {
    type: p.type,
    operator: p.operator,
    formula1: p.formula1,
    formula2: p.formula2,
    allowBlank: p.allowBlank
  }, K = (m) => {
    const A = s.getValidatorItem(m);
    if (!A)
      return;
    const x = A.operators, we = u.getRuleById(i, o, l), J = m === (we == null ? void 0 : we.type) || m.includes("list") && (we != null && we.type.includes("list")) ? {
      ...we,
      type: m
    } : {
      ...p,
      type: m,
      operator: x[0],
      formula1: void 0,
      formula2: void 0
    };
    b(J), h.executeCommand(Mn.id, {
      unitId: i,
      subUnitId: o,
      ruleId: p.uid,
      setting: Dn(J)
    });
  }, z = c.get(y.formulaInput), ve = ct(() => I.map((m) => gr(m.range)).join(","), []), q = Vn(p), G = (m) => {
    In(m, Vn(p)) || (b({
      ...p,
      ...m
    }), _(
      qr.id,
      {
        unitId: i,
        subUnitId: o,
        ruleId: l,
        options: m
      }
    ));
  }, ne = P.length && !p.operator;
  return /* @__PURE__ */ $("div", { "data-u-comp": "data-validation-detail", className: "univer-py-2", children: [
    /* @__PURE__ */ S(
      j,
      {
        label: v.t("dataValidation.panel.range"),
        error: !p.ranges.length || R ? v.t("dataValidation.panel.rangeError") : "",
        children: /* @__PURE__ */ S(
          Vi,
          {
            selectorRef: T,
            unitId: i,
            subUnitId: o,
            initialValue: ve,
            onChange: (m, A) => {
              var x;
              !N && ((x = T.current) != null && x.verify()) && ee(A);
            },
            onFocusChange: (m, A) => {
              var x;
              E(m), !m && A && ((x = T.current) != null && x.verify()) && ee(A);
            },
            onVerify: (m) => L(!m)
          }
        )
      }
    ),
    /* @__PURE__ */ S(j, { label: v.t("dataValidation.panel.type"), children: /* @__PURE__ */ S(
      An,
      {
        className: "univer-w-full",
        value: p.type,
        options: (V = f == null ? void 0 : f.sort((m, A) => m.order - A.order)) == null ? void 0 : V.map((m) => ({
          label: v.t(m.title),
          value: m.id
        })),
        onChange: K
      }
    ) }),
    P != null && P.length ? /* @__PURE__ */ S(j, { label: v.t("dataValidation.panel.operator"), children: /* @__PURE__ */ S(
      An,
      {
        className: "univer-w-full",
        value: `${p.operator}`,
        options: [
          {
            value: "",
            label: v.t("dataValidation.operators.legal")
          },
          ...P.map((m, A) => ({
            value: `${m}`,
            label: k[A]
          }))
        ],
        onChange: (m) => {
          te({
            ...ue,
            operator: m
          });
        }
      }
    ) }) : null,
    z && !ne ? /* @__PURE__ */ S(
      z,
      {
        isTwoFormula: W,
        value: {
          formula1: p.formula1,
          formula2: p.formula2
        },
        onChange: (m) => {
          te({
            ...ue,
            ...m
          });
        },
        showError: g,
        validResult: y.validatorFormula(p, i, o),
        unitId: i,
        subUnitId: o,
        ruleId: l
      },
      e + p.type
    ) : null,
    /* @__PURE__ */ S(j, { children: /* @__PURE__ */ S(
      At,
      {
        checked: (U = p.allowBlank) != null ? U : !0,
        onChange: () => {
          var m;
          return te({
            ...ue,
            allowBlank: !((m = p.allowBlank) == null || m)
          });
        },
        children: v.t("dataValidation.panel.allowBlank")
      }
    ) }),
    /* @__PURE__ */ S(so, { value: q, onChange: G, extraComponent: y.optionsInput }),
    /* @__PURE__ */ $("div", { className: "univer-mt-5 univer-flex univer-flex-row univer-justify-end", children: [
      /* @__PURE__ */ S(Ge, { className: "univer-ml-3", onClick: oe, children: v.t("dataValidation.panel.removeRule") }),
      /* @__PURE__ */ S(Ge, { className: "univer-ml-3", variant: "primary", onClick: X, children: v.t("dataValidation.panel.done") })
    ] })
  ] });
}
const ho = (e) => {
  const { rule: t, onClick: r, unitId: n, subUnitId: i, disable: o } = e, a = F(be), l = F(Q), s = F(yi), d = a.getValidatorItem(t.type), c = pt(void 0), [h, u] = B(!1), v = F(Yn), p = Pe(v.currentTheme$), b = ct(() => {
    var w;
    const g = v.getColorFromTheme("primary.600"), C = v.getColorFromTheme("loop-color.2"), f = (w = v.getColorFromTheme(C)) != null ? w : g, I = new Br(f).toRgb();
    return {
      fill: `rgba(${I.r}, ${I.g}, ${I.b}, 0.1)`,
      stroke: f
    };
  }, [p]), y = (g) => {
    l.executeCommand(ir.id, {
      ruleId: t.uid,
      unitId: n,
      subUnitId: i
    }), g.stopPropagation();
  };
  return ke(() => () => {
    var g;
    c.current && ((g = c.current) == null || g.forEach((C) => {
      C && s.removeShape(C);
    }));
  }, [s]), /* @__PURE__ */ $(
    "div",
    {
      className: Ne(
        `
                  univer-bg-secondary univer-relative univer--ml-2 univer--mr-2 univer-box-border univer-flex
                  univer-w-[287px] univer-cursor-pointer univer-flex-col univer-justify-between univer-overflow-hidden
                  univer-rounded-md univer-p-2 univer-pr-9
                `,
        {
          "hover:univer-bg-gray-50 dark:hover:!univer-bg-gray-700": !o,
          "univer-opacity-50": o
        }
      ),
      onClick: r,
      onMouseEnter: () => {
        o || (u(!0), c.current = t.ranges.map((g) => s.addShape({
          range: g,
          style: b,
          primary: null
        })));
      },
      onMouseLeave: () => {
        var g;
        u(!1), (g = c.current) == null || g.forEach((C) => {
          C && s.removeShape(C);
        }), c.current = void 0;
      },
      children: [
        /* @__PURE__ */ S(
          "div",
          {
            className: "univer-truncate univer-text-sm univer-font-medium univer-leading-[22px] univer-text-gray-900 dark:!univer-text-white",
            children: d == null ? void 0 : d.generateRuleName(t)
          }
        ),
        /* @__PURE__ */ S(
          "div",
          {
            className: "univer-text-secondary univer-truncate univer-text-xs univer-leading-[18px] dark:!univer-text-gray-300",
            children: t.ranges.map((g) => gr(g)).join(",")
          }
        ),
        h ? /* @__PURE__ */ S(
          "div",
          {
            className: "univer-absolute univer-right-2 univer-top-[19px] univer-flex univer-h-5 univer-w-5 univer-items-center univer-justify-center univer-rounded hover:univer-bg-gray-200 dark:!univer-text-gray-300 dark:hover:!univer-bg-gray-700",
            onClick: y,
            children: /* @__PURE__ */ S(_n, {})
          }
        ) : null
      ]
    }
  );
};
function po(e) {
  const t = F(ie), r = F(de), n = F(Q), i = F(ye), o = F(Se), a = F(ce), [l, s] = B([]), { workbook: d } = e, c = Pe(d.activeSheet$, void 0, !0), h = d.getUnitId(), u = c == null ? void 0 : c.getSheetId();
  ke(() => {
    s(t.getRules(h, u));
    const C = t.ruleChange$.subscribe((f) => {
      f.unitId === h && f.subUnitId === u && s(t.getRules(h, u));
    });
    return () => {
      C.unsubscribe();
    };
  }, [h, u, t]);
  const v = async () => {
    const C = er(i), f = {
      unitId: h,
      subUnitId: u,
      rule: C
    };
    await n.executeCommand(dn.id, f), o.setActiveRule({
      unitId: h,
      subUnitId: u,
      rule: C
    });
  }, p = () => {
    n.executeCommand(Jr.id, {
      unitId: h,
      subUnitId: u
    });
  }, y = ((C) => {
    const f = r.getCurrentUnitForType(Y.UNIVER_SHEET), I = f.getActiveSheet(), w = f.getUnitId(), _ = I.getSheetId();
    return C.map((L) => zr(i, w, _, L.ranges) ? { ...L } : { ...L, disable: !0 });
  })(l), g = y == null ? void 0 : y.some((C) => C.disable);
  return /* @__PURE__ */ $("div", { className: "univer-pb-4", children: [
    y == null ? void 0 : y.map((C) => {
      var f;
      return /* @__PURE__ */ S(
        ho,
        {
          unitId: h,
          subUnitId: u,
          onClick: () => {
            C.disable || o.setActiveRule({
              unitId: h,
              subUnitId: u,
              rule: C
            });
          },
          rule: C,
          disable: (f = C.disable) != null ? f : !1
        },
        C.uid
      );
    }),
    /* @__PURE__ */ $("div", { className: "univer-mt-4 univer-flex univer-flex-row univer-justify-end univer-gap-2", children: [
      l.length && !g ? /* @__PURE__ */ S(Ge, { onClick: p, children: a.t("dataValidation.panel.removeAll") }) : null,
      /* @__PURE__ */ S(Ge, { variant: "primary", onClick: v, children: a.t("dataValidation.panel.add") })
    ] })
  ] });
}
const go = () => {
  const e = F(Se), t = Pe(e.activeRule$, e.activeRule), r = F(de), n = Pe(
    () => r.getCurrentTypeOfUnit$(Y.UNIVER_SHEET),
    void 0,
    void 0,
    []
  ), i = Pe(() => {
    var o;
    return (o = n == null ? void 0 : n.activeSheet$) != null ? o : gi(null);
  }, void 0, void 0, []);
  return !n || !i ? null : t && t.subUnitId === i.getSheetId() ? /* @__PURE__ */ S(uo, {}, t.rule.uid) : /* @__PURE__ */ S(po, { workbook: n });
}, vo = (e) => {
  const { isTwoFormula: t = !1, value: r, onChange: n, showError: i, validResult: o } = e, a = F(ce), l = i ? o == null ? void 0 : o.formula1 : "", s = i ? o == null ? void 0 : o.formula2 : "";
  return t ? /* @__PURE__ */ $(ut, { children: [
    /* @__PURE__ */ S(j, { error: l, children: /* @__PURE__ */ S(
      Le,
      {
        className: "univer-w-full",
        placeholder: a.t("dataValidation.panel.formulaPlaceholder"),
        value: r == null ? void 0 : r.formula1,
        onChange: (d) => {
          n == null || n({
            ...r,
            formula1: d
          });
        }
      }
    ) }),
    /* @__PURE__ */ S("div", { className: "-univer-mt-2 univer-mb-1 univer-text-sm univer-text-gray-400", children: a.t("dataValidation.panel.formulaAnd") }),
    /* @__PURE__ */ S(j, { error: s, children: /* @__PURE__ */ S(
      Le,
      {
        className: "univer-w-full",
        placeholder: a.t("dataValidation.panel.formulaPlaceholder"),
        value: r == null ? void 0 : r.formula2,
        onChange: (d) => {
          n == null || n({
            ...r,
            formula2: d
          });
        }
      }
    ) })
  ] }) : /* @__PURE__ */ S(j, { error: l, children: /* @__PURE__ */ S(
    Le,
    {
      className: "univer-w-full",
      placeholder: a.t("dataValidation.panel.formulaPlaceholder"),
      value: r == null ? void 0 : r.formula1,
      onChange: (d) => {
        n == null || n({ formula1: d });
      }
    }
  ) });
};
function fo(e) {
  const { value: t, onChange: r, showError: n, validResult: i } = e, o = F(ce), a = n ? i == null ? void 0 : i.formula1 : "", l = n ? i == null ? void 0 : i.formula2 : "", [s, d] = B(!((t == null ? void 0 : t.formula1) === void 0 && (t == null ? void 0 : t.formula2) === void 0));
  return /* @__PURE__ */ $(ut, { children: [
    /* @__PURE__ */ S(j, { children: /* @__PURE__ */ S(
      At,
      {
        checked: s,
        onChange: (c) => {
          c ? d(!0) : (d(!1), r == null || r({
            ...t,
            formula1: void 0,
            formula2: void 0
          }));
        },
        children: o.t("dataValidation.checkbox.tips")
      }
    ) }),
    s ? /* @__PURE__ */ S(j, { label: o.t("dataValidation.checkbox.checked"), error: a, children: /* @__PURE__ */ S(
      Le,
      {
        className: "univer-w-full",
        placeholder: o.t("dataValidation.panel.valuePlaceholder"),
        value: t == null ? void 0 : t.formula1,
        onChange: (c) => {
          r == null || r({
            ...t,
            formula1: c || void 0
          });
        }
      }
    ) }) : null,
    s ? /* @__PURE__ */ S(j, { label: o.t("dataValidation.checkbox.unchecked"), error: l, children: /* @__PURE__ */ S(
      Le,
      {
        className: "univer-w-full",
        placeholder: o.t("dataValidation.panel.valuePlaceholder"),
        value: t == null ? void 0 : t.formula2,
        onChange: (c) => {
          r == null || r({
            ...t,
            formula2: c || void 0
          });
        }
      }
    ) }) : null
  ] });
}
function mo(e) {
  var h;
  const { unitId: t, subUnitId: r, value: n, onChange: i, showError: o, validResult: a } = e, l = o ? a == null ? void 0 : a.formula1 : void 0, s = pt(null), [d, c] = B(!1);
  return dr((u) => {
    var p;
    ((p = s.current) == null ? void 0 : p.isClickOutSide(u)) && c(!1);
  }), /* @__PURE__ */ S(j, { error: l, children: /* @__PURE__ */ S(
    vr,
    {
      ref: s,
      className: Ne("univer-box-border univer-h-8 univer-w-full univer-cursor-pointer univer-items-center univer-rounded-lg univer-bg-white univer-pt-2 univer-transition-colors hover:univer-border-primary-600 dark:!univer-bg-gray-700 dark:!univer-text-white [&>div:first-child]:univer-px-2.5 [&>div]:univer-h-5 [&>div]:univer-ring-transparent", yt),
      initValue: (h = n == null ? void 0 : n.formula1) != null ? h : "=",
      unitId: t,
      subUnitId: r,
      isFocus: d,
      isSupportAcrossSheet: !0,
      onChange: (u) => {
        const v = (u != null ? u : "").trim();
        v !== (n == null ? void 0 : n.formula1) && (i == null || i({
          ...n,
          formula1: v
        }));
      },
      onFocus: () => c(!0)
    }
  ) });
}
const _o = [
  "#FFFFFF",
  "#FEE7E7",
  "#FEF0E6",
  "#EFFBD0",
  "#E4F4FE",
  "#E8ECFD",
  "#F1EAFA",
  "#FDE8F3",
  "#E5E5E5",
  "#FDCECE",
  "#FDC49B",
  "#DEF6A2",
  "#9FDAFF",
  "#D0D9FB",
  "#E3D5F6",
  "#FBD0E8",
  "#656565",
  "#FE4B4B",
  "#FF8C51",
  "#8BBB11",
  "#0B9EFB",
  "#3A60F7",
  "#9E6DE3",
  "#F248A6"
], So = (e) => {
  const { value: t, onChange: r, disabled: n } = e, [i, o] = B(!1);
  return /* @__PURE__ */ S(
    Mi,
    {
      align: "start",
      disabled: n,
      open: i,
      onOpenChange: o,
      overlay: /* @__PURE__ */ S(
        "div",
        {
          className: "univer-box-border univer-grid univer-w-fit univer-grid-cols-6 univer-flex-wrap univer-gap-2 univer-p-1.5",
          children: _o.map(
            (a) => /* @__PURE__ */ S(
              "div",
              {
                className: Ne("univer-box-border univer-size-4 univer-cursor-pointer univer-rounded", yt),
                style: { background: a },
                onClick: () => {
                  r(a), o(!1);
                }
              },
              a
            )
          )
        }
      ),
      children: /* @__PURE__ */ $(
        "div",
        {
          className: Ne("univer-box-border univer-inline-flex univer-h-8 univer-w-16 univer-cursor-pointer univer-items-center univer-justify-between univer-gap-2 univer-rounded-lg univer-bg-white univer-px-2.5 univer-transition-colors univer-duration-200 hover:univer-border-primary-600 dark:!univer-bg-gray-700 dark:!univer-text-white", yt),
          children: [
            /* @__PURE__ */ S(
              "div",
              {
                className: "univer-box-border univer-h-4 univer-w-4 univer-rounded univer-text-base",
                style: { background: t }
              }
            ),
            /* @__PURE__ */ S(Sn, {})
          ]
        }
      )
    }
  );
}, Un = (e) => {
  const { item: t, commonProps: r, className: n } = e, { onItemChange: i, onItemDelete: o } = r;
  return /* @__PURE__ */ $("div", { className: Ne("univer-flex univer-items-center univer-gap-2", n), children: [
    !t.isRef && /* @__PURE__ */ S("div", { className: Ne("univer-cursor-move", "draggableHandle"), children: /* @__PURE__ */ S(br, {}) }),
    /* @__PURE__ */ S(
      So,
      {
        value: t.color,
        onChange: (a) => {
          i(t.id, t.label, a);
        }
      }
    ),
    /* @__PURE__ */ S(
      Le,
      {
        disabled: t.isRef,
        value: t.label,
        onChange: (a) => {
          i(t.id, a, t.color);
        }
      }
    ),
    t.isRef ? null : /* @__PURE__ */ S(
      "div",
      {
        className: "univer-ml-1 univer-cursor-pointer univer-rounded univer-text-base hover:univer-bg-gray-200",
        children: /* @__PURE__ */ S(_n, { onClick: () => o(t.id) })
      }
    )
  ] });
};
function Co(e) {
  const { value: t, onChange: r = () => {
  }, unitId: n, subUnitId: i, validResult: o, showError: a, ruleId: l } = e, { formula1: s = "", formula2: d = "" } = t || {}, [c, h] = B(() => De(s) ? "1" : "0"), [u, v] = B(c === "1" ? s : "="), [p, b] = B(c === "1" ? s : "="), y = F(ce), g = F(be), C = F(un), f = F(Qr), [I, w] = B(() => d.split(",")), _ = g.getValidatorItem(H.LIST), [R, L] = B([]), [N, E] = B(""), T = a ? o == null ? void 0 : o.formula1 : "", O = ct(() => C.ruleChange$.pipe(cr(16)), []), P = Pe(O), k = Qt(r);
  ke(() => {
    (async () => {
      await new Promise((m) => {
        setTimeout(() => m(!0), 100);
      });
      const V = C.getRuleById(n, i, l), U = V == null ? void 0 : V.formula1;
      if (De(U) && _ && V) {
        const m = await _.getListAsync(V, n, i);
        L(m);
      }
    })();
  }, [C, P, _, l, i, n]), ke(() => {
    De(s) && s !== p && (v(s), b(p));
  }, [p, s]);
  const [W, X] = B(() => {
    const V = c !== "1" ? ei(s) : [], U = d.split(",");
    return V.map((m, A) => ({
      label: m,
      color: U[A] || Te,
      isRef: !1,
      id: Rn(4)
    }));
  }), ee = (V, U, m) => {
    const A = W.find((x) => x.id === V);
    A && (A.label = U, A.color = m, X([...W]));
  }, te = (V) => {
    const U = W.findIndex((m) => m.id === V);
    U !== -1 && (W.splice(U, 1), X([...W]));
  }, oe = d.split(","), ue = ct(() => R.map((V, U) => ({
    label: V,
    color: oe[U] || Te,
    id: `${U}`,
    isRef: !0
  })), [oe, R]), K = (V, U, m) => {
    const A = [...I];
    A[+V] = m, w(A), k({
      formula1: s,
      formula2: A.join(",")
    });
  }, z = () => {
    X([
      ...W,
      {
        label: "",
        color: Te,
        isRef: !1,
        id: Rn(4)
      }
    ]);
  };
  ke(() => {
    if (c === "1")
      return;
    const V = /* @__PURE__ */ new Set(), U = [];
    W.map((m) => ({
      labelList: m.label.split(","),
      item: m
    })).forEach(({ item: m, labelList: A }) => {
      A.forEach((x) => {
        V.has(x) || (V.add(x), U.push({
          label: x,
          color: m.color
        }));
      });
    }), k({
      formula1: Qn(U.map((m) => m.label)),
      formula2: U.map((m) => m.color === Te ? "" : m.color).join(",")
    });
  }, [W, k, c, p, I]);
  const ve = Qt(async (V) => {
    if (!De(V)) {
      k == null || k({
        formula1: "",
        formula2: d
      });
      return;
    }
    f.getFormulaRefCheck(V) ? (k == null || k({
      formula1: De(V) ? V : "",
      formula2: d
    }), E("")) : (k == null || k({
      formula1: "",
      formula2: d
    }), v("="), E(y.t("dataValidation.validFail.formulaError")));
  }), q = pt(null), [G, ne] = B(!1);
  return dr((V) => {
    var m;
    ((m = q.current) == null ? void 0 : m.isClickOutSide(V)) && ne(!1);
  }), /* @__PURE__ */ $(ut, { children: [
    /* @__PURE__ */ S(j, { label: y.t("dataValidation.list.options"), children: /* @__PURE__ */ $(
      vn,
      {
        value: c,
        onChange: (V) => {
          h(V), v(p), V === "1" && k({
            formula1: p === "=" ? "" : p,
            formula2: I.join(",")
          });
        },
        children: [
          /* @__PURE__ */ S(Ae, { value: "0", children: y.t("dataValidation.list.customOptions") }),
          /* @__PURE__ */ S(Ae, { value: "1", children: y.t("dataValidation.list.refOptions") })
        ]
      }
    ) }),
    c === "1" ? /* @__PURE__ */ $(j, { error: T || N || void 0, children: [
      /* @__PURE__ */ S(
        vr,
        {
          ref: q,
          className: Ne("univer-box-border univer-h-8 univer-w-full univer-cursor-pointer univer-items-center univer-rounded-lg univer-bg-white univer-pt-2 univer-transition-colors hover:univer-border-primary-600 dark:!univer-bg-gray-700 dark:!univer-text-white [&>div:first-child]:univer-px-2.5 [&>div]:univer-h-5 [&>div]:univer-ring-transparent", yt),
          initValue: u,
          unitId: n,
          subUnitId: i,
          isFocus: G,
          isSupportAcrossSheet: !0,
          onFocus: () => ne(!0),
          onChange: (V = "") => {
            const U = (V != null ? V : "").trim();
            b(U), ve(U);
          }
        }
      ),
      ue.length > 0 && /* @__PURE__ */ S("div", { className: "univer-mt-3", children: ue.map((V) => /* @__PURE__ */ S(
        Un,
        {
          className: "univer-mb-3",
          item: V,
          commonProps: { onItemChange: K }
        },
        V.id
      )) })
    ] }) : /* @__PURE__ */ S(j, { error: T, children: /* @__PURE__ */ $("div", { className: "-univer-mt-3", children: [
      /* @__PURE__ */ S(
        bi,
        {
          list: W,
          onListChange: X,
          rowHeight: 28,
          margin: [0, 12],
          draggableHandle: ".draggableHandle",
          itemRender: (V) => /* @__PURE__ */ S(
            Un,
            {
              item: V,
              commonProps: {
                onItemChange: ee,
                onItemDelete: te
              }
            },
            V.id
          ),
          idKey: "id"
        }
      ),
      /* @__PURE__ */ $(
        "a",
        {
          className: "univer-text-primary univer-flex univer-w-fit univer-cursor-pointer univer-flex-row univer-items-center univer-rounded univer-p-1 univer-px-2 univer-text-sm hover:univer-bg-primary-50 dark:hover:!univer-bg-gray-800",
          onClick: z,
          children: [
            /* @__PURE__ */ S(Rr, { className: "univer-mr-1" }),
            y.t("dataValidation.list.add")
          ]
        }
      )
    ] }) })
  ] });
}
const wo = [
  [
    or,
    mo
  ],
  [
    Pt,
    vo
  ],
  [
    cn,
    Co
  ],
  [
    ar,
    fo
  ]
], Io = "LIST_RENDER_MODE_OPTION_INPUT";
function Vt(e) {
  var i;
  const { value: t, onChange: r } = e, n = F(ce);
  return /* @__PURE__ */ S(j, { label: n.t("dataValidation.renderMode.label"), children: /* @__PURE__ */ $(vn, { value: `${(i = t.renderMode) != null ? i : se.CUSTOM}`, onChange: (o) => r({ ...t, renderMode: +o }), children: [
    /* @__PURE__ */ S(Ae, { value: `${se.CUSTOM}`, children: n.t("dataValidation.renderMode.chip") }),
    /* @__PURE__ */ S(Ae, { value: `${se.ARROW}`, children: n.t("dataValidation.renderMode.arrow") }),
    /* @__PURE__ */ S(Ae, { value: `${se.TEXT}`, children: n.t("dataValidation.renderMode.text") })
  ] }) });
}
Vt.componentKey = Io;
const Ro = "DATE_SHOW_TIME_OPTION";
function Dt(e) {
  var i;
  const { value: t, onChange: r } = e, n = F(ce);
  return /* @__PURE__ */ S(j, { children: /* @__PURE__ */ S(
    At,
    {
      checked: (i = t.bizInfo) == null ? void 0 : i.showTime,
      onChange: (o) => {
        r({
          ...t,
          bizInfo: {
            ...t.bizInfo,
            showTime: o
          }
        });
      },
      children: n.t("dataValidation.showTime.label")
    }
  ) });
}
Dt.componentKey = Ro;
var yo = Object.getOwnPropertyDescriptor, bo = (e, t, r, n) => {
  for (var i = n > 1 ? void 0 : n ? yo(t, r) : t, o = e.length - 1, a; o >= 0; o--)
    (a = e[o]) && (i = a(i) || i);
  return i;
}, je = (e, t) => (r, n) => t(r, n, e);
const _t = 6;
let tn = class {
  constructor(e, t, r, n, i, o) {
    this._commandService = e, this._univerInstanceService = t, this._formulaService = r, this._themeService = n, this._renderManagerService = i, this._dataValidationModel = o;
  }
  _calc(e, t) {
    var d, c, h;
    const { vt: r, ht: n } = t || {}, i = e.endX - e.startX - _t * 2, o = e.endY - e.startY, a = ((d = t == null ? void 0 : t.fs) != null ? d : 10) * 1.6;
    let l = 0, s = 0;
    switch (r) {
      case _e.TOP:
        s = 0;
        break;
      case _e.BOTTOM:
        s = 0 + (o - a);
        break;
      default:
        s = 0 + (o - a) / 2;
        break;
    }
    switch (n) {
      case Ze.LEFT:
        l = _t;
        break;
      case Ze.RIGHT:
        l = _t + (i - a);
        break;
      default:
        l = _t + (i - a) / 2;
        break;
    }
    return {
      left: e.startX + l,
      top: e.startY + s,
      width: ((c = t == null ? void 0 : t.fs) != null ? c : 10) * 1.6,
      height: ((h = t == null ? void 0 : t.fs) != null ? h : 10) * 1.6
    };
  }
  calcCellAutoHeight(e) {
    var r;
    const { style: t } = e;
    return ((r = t == null ? void 0 : t.fs) != null ? r : 10) * 1.6;
  }
  calcCellAutoWidth(e) {
    var r;
    const { style: t } = e;
    return ((r = t == null ? void 0 : t.fs) != null ? r : 10) * 1.6;
  }
  async _parseFormula(e, t, r) {
    var d, c, h, u, v, p, b, y, g;
    const { formula1: n = ri, formula2: i = ni } = e, o = await this._formulaService.getRuleFormulaResult(t, r, e.uid), a = Ut((h = (c = (d = o == null ? void 0 : o[0]) == null ? void 0 : d.result) == null ? void 0 : c[0]) == null ? void 0 : h[0]), l = Ut((p = (v = (u = o == null ? void 0 : o[1]) == null ? void 0 : u.result) == null ? void 0 : v[0]) == null ? void 0 : p[0]), s = En(String(a)) && En(String(l));
    return {
      formula1: De(n) ? Ut((g = (y = (b = o == null ? void 0 : o[0]) == null ? void 0 : b.result) == null ? void 0 : y[0]) == null ? void 0 : g[0]) : n,
      formula2: De(i) ? l : i,
      isFormulaValid: s
    };
  }
  drawWith(e, t) {
    var T, O, P, k;
    const { style: r, primaryWithCoord: n, unitId: i, subUnitId: o, worksheet: a, row: l, col: s } = t, d = n.isMergedMainCell ? n.mergeInfo : n, c = re(a.getCellRaw(l, s)), h = this._dataValidationModel.getRuleByLocation(i, o, l, s);
    if (!h)
      return;
    const u = this._dataValidationModel.getValidator(h.type);
    if (!u || !((T = u.skipDefaultFontRender) != null && T.call(u, h, c, { unitId: i, subUnitId: o, row: l, column: s })))
      return;
    const v = u.parseFormulaSync(h, i, o), { formula1: p } = v, b = this._calc(d, r), { a: y, d: g } = e.getTransform(), C = yn(b.left, y), f = yn(b.top, g), I = $r.create().composeMatrix({
      left: C,
      top: f,
      scaleX: 1,
      scaleY: 1,
      angle: 0,
      skewX: 0,
      skewY: 0,
      flipX: !1,
      flipY: !1
    }), w = d.endX - d.startX, _ = d.endY - d.startY;
    e.save(), e.beginPath(), e.rect(d.startX, d.startY, w, _), e.clip();
    const R = I.getMatrix();
    e.transform(R[0], R[1], R[2], R[3], R[4], R[5]);
    const L = ((O = r == null ? void 0 : r.fs) != null ? O : 10) * 1.6, N = String(c) === String(p), E = this._themeService.getColorFromTheme("primary.600");
    Hr.drawWith(e, {
      checked: N,
      width: L,
      height: L,
      fill: (k = (P = r == null ? void 0 : r.cl) == null ? void 0 : P.rgb) != null ? k : E
    }), e.restore();
  }
  isHit(e, t) {
    const r = t.primaryWithCoord.isMergedMainCell ? t.primaryWithCoord.mergeInfo : t.primaryWithCoord, n = this._calc(r, t.style), i = n.top, o = n.top + n.height, a = n.left, l = n.left + n.width, { x: s, y: d } = e;
    return s <= l && s >= a && d <= o && d >= i;
  }
  async onPointerDown(e, t) {
    var p;
    if (t.button === 2)
      return;
    const { primaryWithCoord: r, unitId: n, subUnitId: i, worksheet: o, row: a, col: l } = e, s = re(o.getCellRaw(a, l)), d = this._dataValidationModel.getRuleByLocation(n, i, a, l);
    if (!d)
      return;
    const c = this._dataValidationModel.getValidator(d.type);
    if (!c || !((p = c.skipDefaultFontRender) != null && p.call(c, d, s, { unitId: n, subUnitId: i, row: a, column: l })))
      return;
    const { formula1: h, formula2: u } = await this._parseFormula(d, n, i), v = {
      range: {
        startColumn: r.actualColumn,
        endColumn: r.actualColumn,
        startRow: r.actualRow,
        endRow: r.actualRow
      },
      value: {
        v: String(s) === ii(String(h)) ? u : h,
        p: null
      }
    };
    this._commandService.executeCommand(
      st.id,
      v
    );
  }
  onPointerEnter(e, t) {
    var r, n;
    (n = (r = Ke(Y.UNIVER_SHEET, this._univerInstanceService, this._renderManagerService)) == null ? void 0 : r.mainComponent) == null || n.setCursor(ze.POINTER);
  }
  onPointerLeave(e, t) {
    var r, n;
    (n = (r = Ke(Y.UNIVER_SHEET, this._univerInstanceService, this._renderManagerService)) == null ? void 0 : r.mainComponent) == null || n.setCursor(ze.DEFAULT);
  }
};
tn = bo([
  je(0, Q),
  je(1, de),
  je(2, M(ti)),
  je(3, M(Yn)),
  je(4, M(Fe)),
  je(5, M(ie))
], tn);
var Mo = Object.getOwnPropertyDescriptor, Eo = (e, t, r, n) => {
  for (var i = n > 1 ? void 0 : n ? Mo(t, r) : t, o = e.length - 1, a; o >= 0; o--)
    (a = e[o]) && (i = a(i) || i);
  return i;
}, Vo = (e, t) => (r, n) => t(r, n, e);
let Ce = class {
  constructor(e) {
    D(this, "canvasRender", null);
    D(this, "dropdownType");
    D(this, "optionsInput");
    D(this, "formulaInput", cn);
    this.injector = e;
  }
};
Ce = Eo([
  Vo(0, M(ye))
], Ce);
class Do extends Ce {
  constructor() {
    super(...arguments);
    D(this, "id", H.CHECKBOX);
    D(this, "canvasRender", this.injector.createInstance(tn));
    D(this, "formulaInput", ar);
  }
}
class Oo extends Ce {
  constructor() {
    super(...arguments);
    D(this, "id", H.CUSTOM);
    D(this, "formulaInput", or);
  }
}
const To = "data-validation.formula-input";
class Po extends Ce {
  constructor() {
    super(...arguments);
    D(this, "id", H.DATE);
    D(this, "formulaInput", To);
    D(this, "optionsInput", Dt.componentKey);
    D(this, "dropdownType", ge.DATE);
  }
}
class Ao extends Ce {
  constructor() {
    super(...arguments);
    D(this, "id", H.DECIMAL);
    D(this, "formulaInput", Pt);
  }
}
const Lo = 4;
class ko extends jr {
  static drawWith(t, r) {
    const { fontString: n, info: i, fill: o, color: a } = r, { layout: l, text: s } = i;
    t.save(), Kn.drawWith(t, {
      width: l.width,
      height: l.height,
      radius: Lo,
      fill: o || Te
    }), t.font = n, t.fillStyle = a, t.textAlign = "center", t.textBaseline = "middle";
    const d = l.width / 2, c = l.height / 2;
    t.fillText(s, d, c), t.restore();
  }
}
const No = 6, Uo = 2, Fo = 4, xo = 4, nn = 6, Ot = 6, Ve = 14;
function Bo(e, t) {
  const r = Yr.getTextSize(e, t), n = r.width + No * 2, { ba: i, bd: o } = r, a = i + o;
  return {
    width: n,
    height: a + Uo * 2,
    ba: i
  };
}
function $t(e, t, r, n) {
  const i = Ve + nn * 2, o = r - i, a = n - Ot * 2, l = e.map((v) => ({
    layout: Bo(v, t),
    text: v
  })), s = {
    width: 0,
    height: 0,
    items: []
  };
  let d = 0;
  l.forEach((v, p) => {
    const { layout: b } = v, { width: y, height: g } = b;
    d + y <= o && (s.items.push({
      ...v,
      left: d
    }), s.width = d + y, s.height = Math.max(s.height, g), p < l.length - 1 ? d += y + Fo : d += y);
  });
  const c = [s], h = s.height, u = s.width;
  return {
    lines: c,
    totalHeight: h,
    contentWidth: o,
    contentHeight: a,
    cellAutoHeight: h + Ot * 2,
    calcAutoWidth: u + i
  };
}
var Wo = Object.getOwnPropertyDescriptor, $o = (e, t, r, n) => {
  for (var i = n > 1 ? void 0 : n ? Wo(t, r) : t, o = e.length - 1, a; o >= 0; o--)
    (a = e[o]) && (i = a(i) || i);
  return i;
}, St = (e, t) => (r, n) => t(r, n, e);
const Ho = new Path2D("M3.32201 4.84556C3.14417 5.05148 2.85583 5.05148 2.67799 4.84556L0.134292 1.90016C-0.152586 1.56798 0.0505937 1 0.456301 1L5.5437 1C5.94941 1 6.15259 1.56798 5.86571 1.90016L3.32201 4.84556Z");
let rn = class {
  constructor(e, t, r, n) {
    D(this, "zIndex");
    D(this, "_dropdownInfoMap", /* @__PURE__ */ new Map());
    this._commandService = e, this._univerInstanceService = t, this._renderManagerService = r, this._dataValidationModel = n;
  }
  _ensureMap(e) {
    let t = this._dropdownInfoMap.get(e);
    return t || (t = /* @__PURE__ */ new Map(), this._dropdownInfoMap.set(e, t)), t;
  }
  _generateKey(e, t) {
    return `${e}.${t}`;
  }
  _drawDownIcon(e, t, r, n, i) {
    const o = r - Ve + 4;
    let a = 4;
    switch (i) {
      case _e.MIDDLE:
        a = (n - Ve) / 2 + 4;
        break;
      case _e.BOTTOM:
        a = n - Ve + 4;
        break;
    }
    e.save(), e.translateWithPrecision(t.startX + o, t.startY + a), e.fillStyle = "#565656", e.fill(Ho), e.restore();
  }
  // eslint-disable-next-line max-lines-per-function
  drawWith(e, t, r, n) {
    var te, oe;
    const { primaryWithCoord: i, row: o, col: a, style: l, data: s, subUnitId: d } = t, c = i.isMergedMainCell ? i.mergeInfo : i, h = s == null ? void 0 : s.fontRenderExtension, { leftOffset: u = 0, rightOffset: v = 0, topOffset: p = 0, downOffset: b = 0 } = h || {}, y = this._ensureMap(d), g = this._generateKey(o, a), C = this._dataValidationModel.getRuleByLocation(t.unitId, t.subUnitId, o, a);
    if (!C)
      return;
    const f = this._dataValidationModel.getValidator(C.type);
    if (!f)
      return;
    const I = {
      startX: c.startX + u,
      endX: c.endX - v,
      startY: c.startY + p,
      endY: c.endY - b
    }, w = I.endX - I.startX, _ = I.endY - I.startY, { cl: R } = l || {}, L = (te = typeof R == "object" ? R == null ? void 0 : R.rgb : R) != null ? te : "#000", N = Oe(l != null ? l : void 0), { vt: E, ht: T } = l || {}, O = E != null ? E : _e.MIDDLE, P = (oe = re(s)) != null ? oe : "", k = f.parseCellValue(P), W = f.getListWithColorMap(C), X = $t(k, N, w, _);
    this._drawDownIcon(e, I, w, _, O), e.save(), e.translateWithPrecision(I.startX, I.startY), e.beginPath(), e.rect(0, 0, w - Ve, _), e.clip(), e.translateWithPrecision(nn, Ot);
    const ee = (X.contentHeight - X.totalHeight) / 2;
    e.translateWithPrecision(0, ee), X.lines.forEach((ue, K) => {
      e.save();
      const { height: z, items: ve } = ue;
      e.translate(0, K * (z + xo)), ve.forEach((G) => {
        e.save(), e.translateWithPrecision(G.left, 0), ko.drawWith(e, {
          ...N,
          info: G,
          color: L,
          fill: W[G.text]
        }), e.restore();
      }), e.restore();
    }), e.restore(), y.set(g, {
      left: I.startX,
      top: I.startY,
      width: X.contentWidth + nn + Ve,
      height: X.contentHeight + Ot * 2
    });
  }
  calcCellAutoHeight(e) {
    var w;
    const { primaryWithCoord: t, style: r, data: n, row: i, col: o } = e, a = n == null ? void 0 : n.fontRenderExtension, { leftOffset: l = 0, rightOffset: s = 0, topOffset: d = 0, downOffset: c = 0 } = a || {}, h = t.isMergedMainCell ? t.mergeInfo : t, u = {
      startX: h.startX + l,
      endX: h.endX - s,
      startY: h.startY + d,
      endY: h.endY - c
    }, v = this._dataValidationModel.getRuleByLocation(e.unitId, e.subUnitId, i, o);
    if (!v)
      return;
    const p = this._dataValidationModel.getValidator(v.type);
    if (!p)
      return;
    const b = u.endX - u.startX, y = u.endY - u.startY, g = (w = re(n)) != null ? w : "", C = p.parseCellValue(g), f = Oe(r != null ? r : void 0);
    return $t(C, f, b, y).cellAutoHeight;
  }
  calcCellAutoWidth(e) {
    var w;
    const { primaryWithCoord: t, style: r, data: n, row: i, col: o } = e, a = n == null ? void 0 : n.fontRenderExtension, { leftOffset: l = 0, rightOffset: s = 0, topOffset: d = 0, downOffset: c = 0 } = a || {}, h = t.isMergedMainCell ? t.mergeInfo : t, u = {
      startX: h.startX + l,
      endX: h.endX - s,
      startY: h.startY + d,
      endY: h.endY - c
    }, v = this._dataValidationModel.getRuleByLocation(e.unitId, e.subUnitId, i, o);
    if (!v)
      return;
    const p = this._dataValidationModel.getValidator(v.type);
    if (!p)
      return;
    const b = u.endX - u.startX, y = u.endY - u.startY, g = (w = re(n)) != null ? w : "", C = p.parseCellValue(g), f = Oe(r != null ? r : void 0);
    return $t(C, f, b, y).calcAutoWidth;
  }
  isHit(e, t) {
    const { primaryWithCoord: r } = t, n = r.isMergedMainCell ? r.mergeInfo : r, { endX: i } = n, { x: o } = e;
    return o >= i - Ve && o <= i;
  }
  onPointerDown(e, t) {
    if (t.button === 2)
      return;
    const { unitId: r, subUnitId: n, row: i, col: o } = e, a = {
      unitId: r,
      subUnitId: n,
      row: i,
      column: o
    };
    this._commandService.executeCommand(Lt.id, a);
  }
  onPointerEnter(e, t) {
    var r, n;
    return (n = (r = Ke(Y.UNIVER_SHEET, this._univerInstanceService, this._renderManagerService)) == null ? void 0 : r.mainComponent) == null ? void 0 : n.setCursor(ze.POINTER);
  }
  onPointerLeave(e, t) {
    var r, n;
    return (n = (r = Ke(Y.UNIVER_SHEET, this._univerInstanceService, this._renderManagerService)) == null ? void 0 : r.mainComponent) == null ? void 0 : n.setCursor(ze.DEFAULT);
  }
};
rn = $o([
  St(0, Q),
  St(1, de),
  St(2, M(Fe)),
  St(3, M(ie))
], rn);
class jo extends Ce {
  constructor() {
    super(...arguments);
    D(this, "id", H.LIST_MULTIPLE);
    D(this, "canvasRender", this.injector.createInstance(rn));
    D(this, "dropdownType", ge.MULTIPLE_LIST);
  }
}
var Yo = Object.getOwnPropertyDescriptor, Xo = (e, t, r, n) => {
  for (var i = n > 1 ? void 0 : n ? Yo(t, r) : t, o = e.length - 1, a; o >= 0; o--)
    (a = e[o]) && (i = a(i) || i);
  return i;
}, at = (e, t) => (r, n) => t(r, n, e);
const Ee = 6, Ct = 4, pe = 14, Fn = 2, me = 6, Ye = 3, Ht = 4, Zo = "#565656", xn = new Path2D("M3.32201 4.84556C3.14417 5.05148 2.85583 5.05148 2.67799 4.84556L0.134292 1.90016C-0.152586 1.56798 0.0505937 1 0.456301 1L5.5437 1C5.94941 1 6.15259 1.56798 5.86571 1.90016L3.32201 4.84556Z");
function Bn(e, t, r, n, i, o, a = !0) {
  let l = 0;
  const s = a ? Ye : 0;
  switch (i) {
    case _e.BOTTOM:
      l = t - n - s;
      break;
    case _e.MIDDLE:
      l = (t - n) / 2;
      break;
    default:
      l = s;
      break;
  }
  l = Math.max(Ye, l);
  let d = 0;
  switch (o) {
    case Ze.CENTER:
      d = (e - r) / 2;
      break;
    case Ze.RIGHT:
      d = e - r;
      break;
  }
  return d = Math.max(me, d), {
    paddingLeft: d,
    paddingTop: l
  };
}
let on = class {
  constructor(e, t, r, n, i) {
    D(this, "_dropdownInfoMap", /* @__PURE__ */ new Map());
    D(this, "zIndex");
    this._univerInstanceService = e, this._localeService = t, this._commandService = r, this._renderManagerService = n, this._dataValidationModel = i;
  }
  _ensureMap(e) {
    let t = this._dropdownInfoMap.get(e);
    return t || (t = /* @__PURE__ */ new Map(), this._dropdownInfoMap.set(e, t)), t;
  }
  _generateKey(e, t) {
    return `${e}.${t}`;
  }
  _drawDownIcon(e, t, r, n, i, o, a) {
    const { t: l = ae.pd.t, b: s = ae.pd.b } = a, d = r - pe;
    let c;
    switch (o) {
      case _e.MIDDLE:
        c = (n - Ct) / 2;
        break;
      case _e.BOTTOM:
        c = n - s - i - Ye + (i / 2 - Ct / 2);
        break;
      default:
        c = l + Ye + (i / 2 - Ct / 2);
        break;
    }
    e.save(), e.translateWithPrecision(t.startX + d, t.startY + c), e.fillStyle = "#565656", e.fill(xn), e.restore();
  }
  // eslint-disable-next-line max-lines-per-function, complexity
  drawWith(e, t, r) {
    var W, X, ee, te, oe, ue;
    const { primaryWithCoord: n, row: i, col: o, style: a, data: l, subUnitId: s } = t, d = n.isMergedMainCell ? n.mergeInfo : n, c = this._dataValidationModel.getRuleByLocation(t.unitId, t.subUnitId, i, o);
    if (!c)
      return;
    const h = this._dataValidationModel.getValidator(c.type);
    if (!h)
      return;
    const u = l == null ? void 0 : l.fontRenderExtension, { leftOffset: v = 0, rightOffset: p = 0, topOffset: b = 0, downOffset: y = 0 } = u || {};
    if (!c || !h || !h || h.id.indexOf(H.LIST) !== 0 || !h.skipDefaultFontRender(c))
      return;
    const g = {
      startX: d.startX + v,
      endX: d.endX - p,
      startY: d.startY + b,
      endY: d.endY - y
    }, C = g.endX - g.startX, f = g.endY - g.startY, I = this._ensureMap(s), w = this._generateKey(i, o), _ = h.getListWithColor(c), R = re(l), L = `${R != null ? R : ""}`, N = _.find((K) => K.label === L);
    let { tb: E, vt: T, ht: O, pd: P } = a || {};
    E = E != null ? E : fe.WRAP, T = T != null ? T : _e.BOTTOM, O = O != null ? O : ae.ht, P = P != null ? P : ae.pd;
    const k = Oe(a).fontCache;
    if (c.renderMode === se.ARROW) {
      const { l: K = ae.pd.l, t: z = ae.pd.t, r: ve = ae.pd.r, b: q = ae.pd.b } = P, G = C - K - ve - pe - 4, ne = new it(
        L,
        k,
        E === fe.WRAP,
        G,
        1 / 0
      );
      ne.calculate();
      const V = ne.getTotalWidth(), U = ne.getTotalHeight(), { paddingTop: m, paddingLeft: A } = Bn(G, f - z - q, V, U, T, O, !0);
      this._drawDownIcon(e, g, C, f, U, T, P), e.save(), e.translateWithPrecision(g.startX + K, g.startY + z), e.beginPath(), e.rect(0, 0, C - K - ve, f - z - q), e.clip(), e.translateWithPrecision(0, m), e.save(), e.translateWithPrecision(Ee, 0), e.beginPath(), e.rect(0, 0, G, U), e.clip(), bn.drawWith(e, {
        text: L,
        fontStyle: k,
        width: G,
        height: U,
        color: (W = a == null ? void 0 : a.cl) == null ? void 0 : W.rgb,
        strokeLine: !!((X = a == null ? void 0 : a.st) != null && X.s),
        underline: !!((ee = a == null ? void 0 : a.ul) != null && ee.s),
        warp: E === fe.WRAP,
        hAlign: Ze.LEFT
      }, ne), e.translateWithPrecision(A, 0), e.restore(), e.restore(), I.set(w, {
        left: g.endX + K + r.rowHeaderWidth - pe,
        top: g.startY + z + r.columnHeaderHeight,
        width: pe,
        height: f - z - q
      });
    } else {
      e.save(), e.translateWithPrecision(g.startX, g.startY), e.beginPath(), e.rect(0, 0, C, f), e.clip();
      const K = C - me * 2 - Ee - pe - 4, z = new it(
        L,
        k,
        E === fe.WRAP,
        K,
        1 / 0
      );
      z.calculate();
      const ve = z.getTotalWidth(), q = z.getTotalHeight(), G = q + Fn * 2, ne = Math.max(C - me * 2, 1), { paddingTop: V, paddingLeft: U } = Bn(ne, f, ve, G, T, O);
      e.translateWithPrecision(me, V), Kn.drawWith(e, {
        width: ne,
        height: G,
        fill: (N == null ? void 0 : N.color) || Te,
        radius: Ht
      }), e.save(), e.translateWithPrecision(Ee, Fn), e.beginPath(), e.rect(0, 0, K, q), e.clip(), e.translateWithPrecision(U, 0), bn.drawWith(e, {
        text: L,
        fontStyle: k,
        width: K,
        height: q,
        color: (te = a == null ? void 0 : a.cl) == null ? void 0 : te.rgb,
        strokeLine: !!((oe = a == null ? void 0 : a.st) != null && oe.s),
        underline: !!((ue = a == null ? void 0 : a.ul) != null && ue.s),
        warp: E === fe.WRAP,
        hAlign: Ze.LEFT
      }, z), e.restore(), e.translateWithPrecision(K + Ee + 4, (q - Ct) / 2), e.fillStyle = Zo, e.fill(xn), e.restore(), I.set(w, {
        left: g.startX + me + r.rowHeaderWidth,
        top: g.startY + V + r.columnHeaderHeight,
        width: ne,
        height: G
      });
    }
  }
  calcCellAutoHeight(e) {
    const { primaryWithCoord: t, style: r, data: n, row: i, col: o } = e, a = t.isMergedMainCell ? t.mergeInfo : t, l = n == null ? void 0 : n.fontRenderExtension, { leftOffset: s = 0, rightOffset: d = 0, topOffset: c = 0, downOffset: h = 0 } = l || {}, u = this._dataValidationModel.getRuleByLocation(e.unitId, e.subUnitId, i, o);
    if (!u || u.renderMode === se.TEXT)
      return;
    const v = {
      startX: a.startX + s,
      endX: a.endX - d,
      startY: a.startY + c,
      endY: a.endY - h
    }, p = v.endX - v.startX, b = re(n), y = `${b != null ? b : ""}`;
    let { tb: g, pd: C } = r || {};
    const { t: f = ae.pd.t, b: I = ae.pd.b } = C != null ? C : {};
    if (g = g != null ? g : fe.WRAP, u.renderMode === se.ARROW) {
      const w = p - pe, _ = new it(
        y,
        Oe(r).fontCache,
        g === fe.WRAP,
        w,
        1 / 0
      );
      return _.calculate(), _.getTotalHeight() + f + I + Ye * 2;
    } else {
      const w = Math.max(p - me * 2 - Ee - pe, 10), _ = new it(
        y,
        Oe(r).fontCache,
        g === fe.WRAP,
        w,
        1 / 0
      );
      return _.calculate(), _.getTotalHeight() + Ye * 2 + 4;
    }
  }
  calcCellAutoWidth(e) {
    const { primaryWithCoord: t, style: r, data: n, row: i, col: o } = e, a = t.isMergedMainCell ? t.mergeInfo : t, l = n == null ? void 0 : n.fontRenderExtension, { leftOffset: s = 0, rightOffset: d = 0, topOffset: c = 0, downOffset: h = 0 } = l || {}, u = this._dataValidationModel.getRuleByLocation(e.unitId, e.subUnitId, i, o);
    if (!u || u.renderMode === se.TEXT)
      return;
    const v = {
      startX: a.startX + s,
      endX: a.endX - d,
      startY: a.startY + c,
      endY: a.endY - h
    }, p = v.endX - v.startX, b = re(n), y = `${b != null ? b : ""}`;
    let { tb: g, pd: C } = r || {};
    const { l: f = ae.pd.l, r: I = ae.pd.r } = C != null ? C : {};
    g = g != null ? g : fe.WRAP;
    let w = me * 2 + pe;
    switch (u.renderMode) {
      case se.ARROW:
        w = pe + me * 2 + I + f;
        break;
      case se.CUSTOM:
        w = pe + me * 2 + Ee * 2 + I + f + Ht / 2 + 1;
        break;
      // default is CUSTOM
      default:
        w = pe + me * 2 + Ee * 2 + I + f + Ht / 2 + 1;
    }
    const _ = p - w, R = new it(
      y,
      Oe(r).fontCache,
      g === fe.WRAP,
      _,
      1 / 0
    );
    return R.calculate(), R.getTotalWidth() + w;
  }
  isHit(e, t) {
    const { subUnitId: r, row: n, col: i } = t, a = this._ensureMap(r).get(this._generateKey(n, i)), l = this._dataValidationModel.getRuleByLocation(t.unitId, t.subUnitId, n, i);
    if (!l || !a || l.renderMode === se.TEXT)
      return !1;
    const { top: s, left: d, width: c, height: h } = a, { x: u, y: v } = e;
    return u >= d && u <= d + c && v >= s && v <= s + h;
  }
  onPointerDown(e, t) {
    if (t.button === 2)
      return;
    const { unitId: r, subUnitId: n, row: i, col: o } = e, a = {
      unitId: r,
      subUnitId: n,
      row: i,
      column: o
    };
    this._commandService.executeCommand(Lt.id, a);
  }
  onPointerEnter(e, t) {
    var r, n;
    (n = (r = Ke(Y.UNIVER_SHEET, this._univerInstanceService, this._renderManagerService)) == null ? void 0 : r.mainComponent) == null || n.setCursor(ze.POINTER);
  }
  onPointerLeave(e, t) {
    var r, n;
    (n = (r = Ke(Y.UNIVER_SHEET, this._univerInstanceService, this._renderManagerService)) == null ? void 0 : r.mainComponent) == null || n.setCursor(ze.DEFAULT);
  }
};
on = Xo([
  at(0, de),
  at(1, M(ce)),
  at(2, Q),
  at(3, M(Fe)),
  at(4, M(ie))
], on);
class Ko extends Ce {
  constructor() {
    super(...arguments);
    D(this, "id", H.LIST);
    D(this, "canvasRender", this.injector.createInstance(on));
    D(this, "dropdownType", ge.LIST);
    D(this, "optionsInput", Vt.componentKey);
    D(this, "formulaInput", cn);
  }
}
class zo extends Ce {
  constructor() {
    super(...arguments);
    D(this, "id", H.TEXT_LENGTH);
    D(this, "formulaInput", Pt);
  }
}
class Go extends Ce {
  constructor() {
    super(...arguments);
    D(this, "id", H.WHOLE);
    D(this, "formulaInput", Pt);
  }
}
var qo = Object.getOwnPropertyDescriptor, Jo = (e, t, r, n) => {
  for (var i = n > 1 ? void 0 : n ? qo(t, r) : t, o = e.length - 1, a; o >= 0; o--)
    (a = e[o]) && (i = a(i) || i);
  return i;
}, jt = (e, t) => (r, n) => t(r, n, e);
let nt = class extends sn {
  constructor(e, t, r) {
    super(), this._injector = e, this._componentManger = t, this._dataValidatorRegistryService = r, this._initComponents(), this._registerValidatorViews();
  }
  _initComponents() {
    [
      ["DataValidationIcon", Ir],
      [Mt, go],
      [Vt.componentKey, Vt],
      [Dt.componentKey, Dt],
      ...wo
    ].forEach(([e, t]) => {
      this.disposeWithMe(this._componentManger.register(
        e,
        t
      ));
    });
  }
  _registerValidatorViews() {
    [
      Ao,
      Go,
      zo,
      Po,
      Do,
      Ko,
      jo,
      Oo
    ].forEach((e) => {
      const t = this._injector.createInstance(e), r = this._dataValidatorRegistryService.getValidatorItem(t.id);
      r && (r.formulaInput = t.formulaInput, r.canvasRender = t.canvasRender, r.dropdownType = t.dropdownType, r.optionsInput = t.optionsInput);
    });
  }
};
nt = Jo([
  jt(0, M(ye)),
  jt(1, M(pn)),
  jt(2, M(be))
], nt);
var Qo = Object.getOwnPropertyDescriptor, ea = (e, t, r, n) => {
  for (var i = n > 1 ? void 0 : n ? Qo(t, r) : t, o = e.length - 1, a; o >= 0; o--)
    (a = e[o]) && (i = a(i) || i);
  return i;
}, Yt = (e, t) => (r, n) => t(r, n, e);
const ta = "SHEET_DATA_VALIDATION_UI_PLUGIN";
var wt;
let Wn = (wt = class extends Xn {
  constructor(e = bt, t, r, n) {
    super(), this._config = e, this._injector = t, this._commandService = r, this._configService = n;
    const { menu: i, ...o } = Zn(
      {},
      bt,
      this._config
    );
    i && this._configService.setConfig("menu", i, { merge: !0 }), this._configService.setConfig(fn, o);
  }
  onStarting() {
    [
      [Se],
      [Ue],
      [ht],
      [Je],
      [tt],
      [et],
      [Qe],
      [nt]
    ].forEach((e) => {
      this._injector.add(e);
    }), [
      kt,
      Lt,
      mr,
      mn,
      Be,
      fr
    ].forEach((e) => {
      this._commandService.registerCommand(e);
    });
  }
  onReady() {
    this._injector.get(Qe), this._injector.get(et), this._injector.get(Fe).registerRenderModule(
      Y.UNIVER_SHEET,
      [Et]
    );
  }
  onRendered() {
    this._injector.get(nt), this._injector.get(tt);
  }
  onSteady() {
    this._injector.get(Je);
  }
}, D(wt, "pluginName", ta), D(wt, "type", Y.UNIVER_SHEET), wt);
Wn = ea([
  Yt(1, M(ye)),
  Yt(2, Q),
  Yt(3, an)
], Wn);
var na = Object.defineProperty, ra = Object.getOwnPropertyDescriptor, ia = (e, t, r) => t in e ? na(e, t, { enumerable: !0, configurable: !0, writable: !0, value: r }) : e[t] = r, oa = (e, t, r, n) => {
  for (var i = n > 1 ? void 0 : n ? ra(t, r) : t, o = e.length - 1, a; o >= 0; o--)
    (a = e[o]) && (i = a(i) || i);
  return i;
}, Xt = (e, t) => (r, n) => t(r, n, e), Mr = (e, t, r) => ia(e, typeof t != "symbol" ? t + "" : t, r);
const aa = "SHEET_DATA_VALIDATION_UI_PLUGIN";
let Tt = class extends Xn {
  constructor(e = bt, t, r, n) {
    super(), this._config = e, this._injector = t, this._commandService = r, this._configService = n;
    const { menu: i, ...o } = Zn(
      {},
      bt,
      this._config
    );
    i && this._configService.setConfig("menu", i, { merge: !0 }), this._configService.setConfig(fn, o);
  }
  onStarting() {
    [
      [Se],
      [Ue],
      [ht],
      [Je],
      [tt],
      [et],
      [Qe],
      [qe],
      [nt]
    ].forEach((e) => {
      this._injector.add(e);
    }), [
      kt,
      Lt,
      mr,
      mn,
      Be,
      fr
    ].forEach((e) => {
      this._commandService.registerCommand(e);
    });
  }
  onReady() {
    this._injector.get(Qe), this._injector.get(et), this._injector.get(qe), this._injector.get(ht), this._injector.get(Fe).registerRenderModule(
      Y.UNIVER_SHEET,
      [Et]
    );
  }
  onRendered() {
    this._injector.get(nt), this._injector.get(tt);
  }
  onSteady() {
    this._injector.get(Je);
  }
};
Mr(Tt, "pluginName", aa);
Mr(Tt, "type", Y.UNIVER_SHEET);
Tt = oa([
  Wr(oi),
  Xt(1, M(ye)),
  Xt(2, Q),
  Xt(3, an)
], Tt);
export {
  Ce as BaseSheetDataValidatorView,
  Wn as UniverSheetsDataValidationMobileUIPlugin,
  Tt as UniverSheetsDataValidationUIPlugin
};
