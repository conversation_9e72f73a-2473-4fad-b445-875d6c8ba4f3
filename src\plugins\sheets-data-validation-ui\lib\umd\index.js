(function(U,o){typeof exports=="object"&&typeof module<"u"?o(exports,require("@univerjs/core"),require("@univerjs/engine-render"),require("@univerjs/sheets"),require("@univerjs/sheets-data-validation"),require("@univerjs/data-validation"),require("@univerjs/ui"),require("rxjs"),require("@univerjs/sheets-numfmt"),require("@univerjs/sheets-ui"),require("@univerjs/design"),require("react"),require("react/jsx-runtime"),require("@univerjs/engine-formula"),require("@univerjs/sheets-formula-ui")):typeof define=="function"&&define.amd?define(["exports","@univerjs/core","@univerjs/engine-render","@univerjs/sheets","@univerjs/sheets-data-validation","@univerjs/data-validation","@univerjs/ui","rxjs","@univerjs/sheets-numfmt","@univerjs/sheets-ui","@univerjs/design","react","react/jsx-runtime","@univerjs/engine-formula","@univerjs/sheets-formula-ui"],o):(U=typeof globalThis<"u"?globalThis:U||self,o(U.UniverSheetsDataValidationUi={},U.UniverCore,U.UniverEngineRender,U.UniverSheets,U.UniverSheetsDataValidation,U.UniverDataValidation,U.UniverUi,U.rxjs,U.UniverSheetsNumfmt,U.UniverSheetsUi,U.UniverDesign,U.React,U.React,U.UniverEngineFormula,U.UniverSheetsFormulaUi))})(this,function(U,o,T,$,S,X,V,ie,Tt,B,b,R,v,Xe,ze){"use strict";var cr=Object.defineProperty;var ur=(U,o,T)=>o in U?cr(U,o,{enumerable:!0,configurable:!0,writable:!0,value:T}):U[o]=T;var A=(U,o,T)=>ur(U,typeof o!="symbol"?o+"":o,T);var Ye;var Ot=Object.getOwnPropertyDescriptor,Pt=(e,t,r,n)=>{for(var a=n>1?void 0:n?Ot(t,r):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(a=s(a)||a);return a},ut=(e,t)=>(r,n)=>t(r,n,e);let ue=class extends o.Disposable{constructor(t,r){super();A(this,"_open$",new ie.BehaviorSubject(!1));A(this,"open$",this._open$.pipe(ie.distinctUntilChanged()));A(this,"_activeRule");A(this,"_activeRule$",new ie.BehaviorSubject(void 0));A(this,"activeRule$",this._activeRule$.asObservable());A(this,"_closeDisposable",null);this._univerInstanceService=t,this._sidebarService=r,this.disposeWithMe(this._univerInstanceService.getCurrentTypeOfUnit$(o.UniverInstanceType.UNIVER_SHEET).pipe(ie.filter(n=>!n)).subscribe(()=>{this.close()})),this.disposeWithMe(this._sidebarService.sidebarOptions$.subscribe(n=>{n.id===Ue&&(n.visible||setTimeout(()=>{this._sidebarService.sidebarOptions$.next({visible:!1})}))}))}get activeRule(){return this._activeRule}get isOpen(){return this._open$.getValue()}dispose(){var t;super.dispose(),this._open$.next(!1),this._open$.complete(),this._activeRule$.complete(),(t=this._closeDisposable)==null||t.dispose()}open(){this._open$.next(!0)}close(){var t;this._open$.next(!1),(t=this._closeDisposable)==null||t.dispose()}setCloseDisposable(t){this._closeDisposable=o.toDisposable(()=>{t.dispose(),this._closeDisposable=null})}setActiveRule(t){this._activeRule=t,this._activeRule$.next(t)}};ue=Pt([ut(0,o.IUniverInstanceService),ut(1,V.ISidebarService)],ue);const ge="#ECECEC",Ke="sheets-data-validation-ui.config",Ae={};var Lt=Object.getOwnPropertyDescriptor,At=(e,t,r,n)=>{for(var a=n>1?void 0:n?Lt(t,r):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(a=s(a)||a);return a},ye=(e,t)=>(r,n)=>t(r,n,e);let we=class extends o.Disposable{constructor(e,t,r,n,a,i){super(),this._sheetInterceptorService=e,this._dataValidationModel=t,this._dataValidatorRegistryService=r,this._dialogService=n,this._localeService=a,this._sheetsDataValidationValidatorService=i,this._initEditorBridgeInterceptor()}_initEditorBridgeInterceptor(){this._sheetInterceptorService.writeCellInterceptor.intercept($.VALIDATE_CELL,{handler:async(e,t,r)=>{const n=await e,{row:a,col:i,unitId:s,subUnitId:d}=t,l=this._dataValidationModel.getRuleIdByLocation(s,d,a,i),c=l?this._dataValidationModel.getRuleById(s,d,l):void 0;if(n===!1)return r(Promise.resolve(!1));if(!c||c.errorStyle!==o.DataValidationErrorStyle.STOP)return r(Promise.resolve(!0));const u=this._dataValidatorRegistryService.getValidatorItem(c.type);return!u||await this._sheetsDataValidationValidatorService.validatorCell(s,d,a,i)===o.DataValidationStatus.VALID?r(Promise.resolve(!0)):(this._dialogService.open({width:368,title:{title:this._localeService.t("dataValidation.alert.title")},id:"reject-input-dialog",children:{title:u.getRuleFinalError(c,{row:a,col:i,unitId:s,subUnitId:d})},footer:{title:R.createElement(b.Button,{variant:"primary",onClick:()=>this._dialogService.close("reject-input-dialog")},this._localeService.t("dataValidation.alert.ok"))},onClose:()=>{this._dialogService.close("reject-input-dialog")}}),r(Promise.resolve(!1)))}})}showReject(e){this._dialogService.open({width:368,title:{title:this._localeService.t("dataValidation.alert.title")},id:"reject-input-dialog",children:{title:e},footer:{title:R.createElement(b.Button,{variant:"primary",onClick:()=>this._dialogService.close("reject-input-dialog")},this._localeService.t("dataValidation.alert.ok"))},onClose:()=>{this._dialogService.close("reject-input-dialog")}})}};we=At([ye(0,o.Inject($.SheetInterceptorService)),ye(1,o.Inject(S.SheetDataValidationModel)),ye(2,o.Inject(X.DataValidatorRegistryService)),ye(3,V.IDialogService),ye(4,o.Inject(o.LocaleService)),ye(5,o.Inject(S.SheetsDataValidationValidatorService))],we);var Ut=Object.getOwnPropertyDescriptor,Ft=(e,t,r,n)=>{for(var a=n>1?void 0:n?Ut(t,r):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(a=s(a)||a);return a},le=(e,t)=>(r,n)=>t(r,n,e);const Ze=e=>{if(e==null||typeof e=="boolean")return;if(typeof e=="number"||!Number.isNaN(+e))return o.dayjs(o.numfmt.format("yyyy-MM-dd HH:mm:ss",Number(e)));const t=o.dayjs(e);if(t.isValid())return t};function kt(e,t){const r=Tt.getPatternType(t);if(e===r)return t;switch(e){case"datetime":return"yyyy-MM-dd hh:mm:ss";case"date":return"yyyy-MM-dd";case"time":return"HH:mm:ss"}}let fe=class extends o.Disposable{constructor(t,r,n,a,i,s,d,l,c,u,p){super();A(this,"_activeDropdown");A(this,"_activeDropdown$",new ie.Subject);A(this,"_currentPopup",null);A(this,"activeDropdown$",this._activeDropdown$.asObservable());A(this,"_zenVisible",!1);this._univerInstanceService=t,this._dataValidatorRegistryService=r,this._zenZoneService=n,this._dataValidationModel=a,this._sheetsSelectionsService=i,this._cellDropdownManagerService=s,this._sheetDataValidationModel=d,this._commandService=l,this._editorBridgeService=c,this._injector=u,this._configService=p,this._init(),this._initSelectionChange(),this.disposeWithMe(()=>{this._activeDropdown$.complete()})}get activeDropdown(){return this._activeDropdown}_init(){this.disposeWithMe(this._zenZoneService.visible$.subscribe(t=>{this._zenVisible=t,t&&this.hideDropdown()}))}_getDropdownByCell(t,r,n,a){const i=t?this._univerInstanceService.getUnit(t,o.UniverInstanceType.UNIVER_SHEET):this._univerInstanceService.getCurrentUnitForType(o.UniverInstanceType.UNIVER_SHEET);if(!i)return;const s=r?i.getSheetBySheetId(r):i.getActiveSheet();if(!s)return;const d=this._dataValidationModel.getRuleByLocation(i.getUnitId(),s.getSheetId(),n,a);if(!d)return;const l=this._dataValidatorRegistryService.getValidatorItem(d.type);return l==null?void 0:l.dropdownType}_initSelectionChange(){this.disposeWithMe(this._sheetsSelectionsService.selectionMoveEnd$.subscribe(t=>{t&&t.every(r=>!(r.primary&&this._getDropdownByCell(r.primary.unitId,r.primary.sheetId,r.primary.actualRow,r.primary.actualColumn)))&&this.hideDropdown()}))}showDropdown(t){var O,M,f,y;const{location:r}=t,{row:n,col:a,unitId:i,subUnitId:s,workbook:d,worksheet:l}=r;if(this._currentPopup&&this._currentPopup.dispose(),this._zenVisible)return;this._activeDropdown=t,this._activeDropdown$.next(this._activeDropdown);const c=this._sheetDataValidationModel.getRuleByLocation(i,s,n,a);if(!c)return;const u=this._dataValidatorRegistryService.getValidatorItem(c.type);if(!(u!=null&&u.dropdownType))return;let p;const h=async(_,D)=>{var F,N,x;if(!_)return!0;const w=_,C=l.getCell(n,a),E=w.format(D==="date"?"YYYY-MM-DD 00:00:00":"YYYY-MM-DD HH:mm:ss"),W=(F=o.numfmt.parseDate(E))==null?void 0:F.v,H=D==="time"?W%1:W,P=d.getStyles().getStyleByCell(C),k=(x=(N=P==null?void 0:P.n)==null?void 0:N.pattern)!=null?x:"";return c.errorStyle!==o.DataValidationErrorStyle.STOP||await u.validator({value:H,unitId:i,subUnitId:s,row:n,column:a,worksheet:l,workbook:d,interceptValue:E.replace("Z","").replace("T"," "),t:o.CellValueType.NUMBER},c)?(await this._commandService.executeCommand($.SetRangeValuesCommand.id,{unitId:i,subUnitId:s,range:{startColumn:a,endColumn:a,startRow:n,endRow:n},value:{v:H,t:2,p:null,f:null,si:null,s:{n:{pattern:kt(D,k)}}}}),await this._commandService.executeCommand(B.SetCellEditVisibleOperation.id,{visible:!1,eventType:T.DeviceInputEventType.Keyboard,unitId:i,keycode:V.KeyCode.ESC}),!0):(this._injector.has(we)&&this._injector.get(we).showReject(u.getRuleFinalError(c,{row:n,col:a,unitId:i,subUnitId:s})),!1)};let m;switch(u.dropdownType){case X.DataValidatorDropdownType.DATE:{const _=S.getCellValueOrigin(l.getCellRaw(n,a)),D=Ze(_),w=!!((O=c.bizInfo)!=null&&O.showTime);m={location:r,type:"datepicker",props:{showTime:w,onChange:C=>h(C,w?"datetime":"date"),defaultValue:D,patternType:"date"}};break}case X.DataValidatorDropdownType.TIME:{const _=S.getCellValueOrigin(l.getCellRaw(n,a)),D=Ze(_);m={location:r,type:"datepicker",props:{onChange:w=>h(w,"time"),defaultValue:D,patternType:"time"}};break}case X.DataValidatorDropdownType.DATETIME:{const _=S.getCellValueOrigin(l.getCellRaw(n,a)),D=Ze(_);m={location:r,type:"datepicker",props:{onChange:w=>h(w,"datetime"),defaultValue:D,patternType:"datetime"}};break}case X.DataValidatorDropdownType.LIST:case X.DataValidatorDropdownType.MULTIPLE_LIST:{const _=u.dropdownType===X.DataValidatorDropdownType.MULTIPLE_LIST,D=async P=>{const k=S.serializeListOptions(P),F={unitId:i,subUnitId:s,range:{startColumn:a,endColumn:a,startRow:n,endRow:n},value:{v:k,p:null,f:null,si:null}};return this._commandService.executeCommand($.SetRangeValuesCommand.id,F),this._editorBridgeService.isVisible().visible&&await this._commandService.executeCommand(B.SetCellEditVisibleOperation.id,{visible:!1,eventType:T.DeviceInputEventType.Keyboard,unitId:i,keycode:V.KeyCode.ESC}),!_},w=(c==null?void 0:c.renderMode)===o.DataValidationRenderMode.CUSTOM||(c==null?void 0:c.renderMode)===void 0,C=u.getListWithColor(c,i,s),E=S.getDataValidationCellValue(l.getCellRaw(n,a)),W=()=>{this._commandService.executeCommand(me.id,{ruleId:c.uid}),p==null||p.dispose()},H=C.map(P=>({label:P.label,value:P.label,color:w||P.color?P.color||ge:"transparent"}));m={location:r,type:"list",props:{onChange:P=>D(P),options:H,onEdit:W,defaultValue:E,multiple:_,showEdit:(f=(M=this._configService.getConfig(Ke))==null?void 0:M.showEditOnDropdown)!=null?f:!0}};break}case X.DataValidatorDropdownType.CASCADE:{m={type:"cascader",props:{onChange:D=>{const w={unitId:i,subUnitId:s,range:{startColumn:a,endColumn:a,startRow:n,endRow:n},value:{v:D.join("/"),p:null,f:null,si:null}};return this._commandService.syncExecuteCommand($.SetRangeValuesCommand.id,w),this._editorBridgeService.isVisible().visible&&this._commandService.syncExecuteCommand(B.SetCellEditVisibleOperation.id,{visible:!1,eventType:T.DeviceInputEventType.Keyboard,unitId:i,keycode:V.KeyCode.ESC}),!0},defaultValue:S.getDataValidationCellValue(l.getCellRaw(n,a)).split("/"),options:JSON.parse((y=c.formula1)!=null?y:"[]")},location:r};break}case X.DataValidatorDropdownType.COLOR:{m={type:"color",props:{onChange:D=>{const w={unitId:i,subUnitId:s,range:{startColumn:a,endColumn:a,startRow:n,endRow:n},value:{v:D,p:null,f:null,si:null}};return this._commandService.syncExecuteCommand($.SetRangeValuesCommand.id,w),this._editorBridgeService.isVisible().visible&&this._commandService.syncExecuteCommand(B.SetCellEditVisibleOperation.id,{visible:!1,eventType:T.DeviceInputEventType.Keyboard,unitId:i,keycode:V.KeyCode.ESC}),!0},defaultValue:S.getDataValidationCellValue(l.getCellRaw(n,a))},location:r};break}default:throw new Error("[DataValidationDropdownManagerService]: unknown type!")}if(p=this._cellDropdownManagerService.showDropdown({...m,onHide:()=>{this._activeDropdown=null,this._activeDropdown$.next(null)}}),!p)throw new Error("[DataValidationDropdownManagerService]: cannot show dropdown!");const g=new o.DisposableCollection;g.add(p),g.add({dispose:()=>{var _,D;(D=(_=this._activeDropdown)==null?void 0:_.onHide)==null||D.call(_)}}),this._currentPopup=g}hideDropdown(){this._activeDropdown&&(this._currentPopup&&this._currentPopup.dispose(),this._currentPopup=null,this._activeDropdown=null,this._activeDropdown$.next(null))}showDataValidationDropdown(t,r,n,a,i){const s=this._univerInstanceService.getUnit(t,o.UniverInstanceType.UNIVER_SHEET);if(!s)return;const d=s.getSheetBySheetId(r);if(!d)return;const l=this._dataValidationModel.getRuleByLocation(s.getUnitId(),d.getSheetId(),n,a);if(!l)return;const c=this._dataValidatorRegistryService.getValidatorItem(l.type);if(!c||!c.dropdownType){this.hideDropdown();return}this.showDropdown({location:{workbook:s,worksheet:d,row:n,col:a,unitId:t,subUnitId:r},onHide:i})}};fe=Ft([le(0,o.IUniverInstanceService),le(1,o.Inject(X.DataValidatorRegistryService)),le(2,V.IZenZoneService),le(3,o.Inject(S.SheetDataValidationModel)),le(4,o.Inject($.SheetsSelectionsService)),le(5,o.Inject(B.ISheetCellDropdownManagerService)),le(6,o.Inject(S.SheetDataValidationModel)),le(7,o.ICommandService),le(8,B.IEditorBridgeService),le(9,o.Inject(o.Injector)),le(10,o.IConfigService)],fe);const Ue="DataValidationPanel",me={id:"data-validation.operation.open-validation-panel",type:o.CommandType.OPERATION,handler(e,t){if(!t)return!1;const{ruleId:r,isAdd:n}=t,a=e.get(ue),i=e.get(X.DataValidationModel),s=e.get(o.IUniverInstanceService),d=e.get(V.ISidebarService),l=$.getSheetCommandTarget(s);if(!l)return!1;const{unitId:c,subUnitId:u}=l,p=r?i.getRuleById(c,u,r):void 0;a.open(),a.setActiveRule(p&&{unitId:c,subUnitId:u,rule:p});const h=d.open({id:Ue,header:{title:n?"dataValidation.panel.addTitle":"dataValidation.panel.title"},children:{label:Ue},width:312,onClose:()=>a.close()});return a.setCloseDisposable(h),!0}},Ge={id:"data-validation.operation.close-validation-panel",type:o.CommandType.OPERATION,handler(e){return e.get(ue).close(),!0}},ht={id:"data-validation.operation.toggle-validation-panel",type:o.CommandType.OPERATION,handler(e){const t=e.get(o.ICommandService),r=e.get(ue);return r.open(),r.isOpen?t.executeCommand(Ge.id):t.executeCommand(me.id),!0}},Fe={type:o.CommandType.OPERATION,id:"sheet.operation.show-data-validation-dropdown",handler(e,t){if(!t)return!1;const r=e.get(fe),{unitId:n,subUnitId:a,row:i,column:s}=t,d=r.activeDropdown,l=d==null?void 0:d.location;return l&&l.unitId===n&&l.subUnitId===a&&l.row===i&&l.col===s||r.showDataValidationDropdown(n,a,i,s),!0}},vt={type:o.CommandType.OPERATION,id:"sheet.operation.hide-data-validation-dropdown",handler(e,t){return t?(e.get(fe).hideDropdown(),!0):!1}},ke={type:o.CommandType.COMMAND,id:"data-validation.command.addRuleAndOpen",handler(e){const t=e.get(o.IUniverInstanceService),r=$.getSheetCommandTarget(t);if(!r)return!1;const{workbook:n,worksheet:a}=r,i=S.createDefaultNewRule(e),s=e.get(o.ICommandService),d=n.getUnitId(),l=a.getSheetId(),c={rule:i,unitId:d,subUnitId:l};return s.syncExecuteCommand(S.AddSheetDataValidationCommand.id,c)?(s.syncExecuteCommand(me.id,{ruleId:i.uid,isAdd:!0}),!0):!1}};var Nt=Object.getOwnPropertyDescriptor,jt=(e,t,r,n)=>{for(var a=n>1?void 0:n?Nt(t,r):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(a=s(a)||a);return a},De=(e,t)=>(r,n)=>t(r,n,e);const Se="SHEET_DATA_VALIDATION_ALERT";let Pe=class extends o.Disposable{constructor(e,t,r,n,a,i){super(),this._hoverManagerService=e,this._cellAlertManagerService=t,this._univerInstanceService=r,this._localeService=n,this._zenZoneService=a,this._dataValidationModel=i,this._init()}_init(){this._initCellAlertPopup(),this._initZenService()}_initCellAlertPopup(){this.disposeWithMe(this._hoverManagerService.currentCell$.pipe(ie.debounceTime(100)).subscribe(e=>{var t;if(e){const r=this._univerInstanceService.getUnit(e.location.unitId,o.UniverInstanceType.UNIVER_SHEET),n=r.getSheetBySheetId(e.location.subUnitId);if(!n)return;const a=this._dataValidationModel.getRuleByLocation(e.location.unitId,e.location.subUnitId,e.location.row,e.location.col);if(!a){this._cellAlertManagerService.removeAlert(Se);return}if(this._dataValidationModel.validator(a,{...e.location,workbook:r,worksheet:n})===o.DataValidationStatus.INVALID){const s=this._cellAlertManagerService.currentAlert.get(Se),d=(t=s==null?void 0:s.alert)==null?void 0:t.location;if(d&&d.row===e.location.row&&d.col===e.location.col&&d.subUnitId===e.location.subUnitId&&d.unitId===e.location.unitId){this._cellAlertManagerService.removeAlert(Se);return}const l=this._dataValidationModel.getValidator(a.type);if(!l){this._cellAlertManagerService.removeAlert(Se);return}this._cellAlertManagerService.showAlert({type:B.CellAlertType.ERROR,title:this._localeService.t("dataValidation.error.title"),message:l==null?void 0:l.getRuleFinalError(a,e.location),location:e.location,width:200,height:74,key:Se});return}}this._cellAlertManagerService.removeAlert(Se)}))}_initZenService(){this.disposeWithMe(this._zenZoneService.visible$.subscribe(e=>{e&&this._cellAlertManagerService.removeAlert(Se)}))}};Pe=jt([De(0,o.Inject(B.HoverManagerService)),De(1,o.Inject(B.CellAlertManagerService)),De(2,o.IUniverInstanceService),De(3,o.Inject(o.LocaleService)),De(4,V.IZenZoneService),De(5,o.Inject(S.SheetDataValidationModel))],Pe);var Bt=Object.getOwnPropertyDescriptor,Wt=(e,t,r,n)=>{for(var a=n>1?void 0:n?Bt(t,r):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(a=s(a)||a);return a},qe=(e,t)=>(r,n)=>t(r,n,e);let Ee=class extends o.Disposable{constructor(e,t,r){super(),this._autoFillService=e,this._sheetDataValidationModel=t,this._injector=r,this._initAutoFill()}_initAutoFill(){const e=()=>({redos:[],undos:[]}),t=(n,a)=>{const{source:i,target:s,unitId:d,subUnitId:l}=n,c=this._sheetDataValidationModel.getRuleObjectMatrix(d,l).clone(),u=B.virtualizeDiscreteRanges([i,s]),[p,h]=u.ranges,{mapFunc:m}=u,g={row:p.startRow,col:p.startColumn},O=B.getAutoFillRepeatRange(p,h),M=new o.ObjectMatrix,f=new Set;O.forEach(C=>{const E=C.repeatStartCell,W=C.relativeRange,H={startRow:g.row,startColumn:g.col,endColumn:g.col,endRow:g.row},P={startRow:E.row,startColumn:E.col,endColumn:E.col,endRow:E.row};o.Range.foreach(W,(k,F)=>{const N=o.Rectangle.getPositionRange({startRow:k,startColumn:F,endColumn:F,endRow:k},H),{row:x,col:K}=m(N.startRow,N.startColumn),G=this._sheetDataValidationModel.getRuleIdByLocation(d,l,x,K)||"",ne=o.Rectangle.getPositionRange({startRow:k,startColumn:F,endColumn:F,endRow:k},P),{row:re,col:se}=m(ne.startRow,ne.startColumn);M.setValue(re,se,G),f.add(G)})});const y=Array.from(f).map(C=>({id:C,ranges:o.queryObjectMatrix(M,E=>E===C)}));c.addRangeRules(y);const _=c.diff(this._sheetDataValidationModel.getRules(d,l)),{redoMutations:D,undoMutations:w}=S.getDataValidationDiffMutations(d,l,_,this._injector,"patched",a===B.APPLY_TYPE.ONLY_FORMAT);return{undos:w,redos:D}},r={id:S.DATA_VALIDATION_PLUGIN_NAME,onBeforeFillData:n=>{const{source:a,unitId:i,subUnitId:s}=n;for(const d of a.rows)for(const l of a.cols){const c=this._sheetDataValidationModel.getRuleByLocation(i,s,d,l);if(c&&c.type===o.DataValidationType.CHECKBOX){this._autoFillService.setDisableApplyType(B.APPLY_TYPE.SERIES,!0);return}}},onFillData:(n,a,i)=>i===B.APPLY_TYPE.COPY||i===B.APPLY_TYPE.ONLY_FORMAT||i===B.APPLY_TYPE.SERIES?t(n,i):e(),onAfterFillData:()=>{}};this.disposeWithMe(this._autoFillService.addHook(r))}};Ee=Wt([qe(0,B.IAutoFillService),qe(1,o.Inject(S.SheetDataValidationModel)),qe(2,o.Inject(o.Injector))],Ee);var xt=Object.getOwnPropertyDescriptor,Ht=(e,t,r,n)=>{for(var a=n>1?void 0:n?xt(t,r):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(a=s(a)||a);return a},Je=(e,t)=>(r,n)=>t(r,n,e);let Me=class extends o.Disposable{constructor(t,r,n){super();A(this,"_copyInfo");this._sheetClipboardService=t,this._sheetDataValidationModel=r,this._injector=n,this._initCopyPaste()}_initCopyPaste(){this._sheetClipboardService.addClipboardHook({id:S.DATA_VALIDATION_PLUGIN_NAME,onBeforeCopy:(t,r,n)=>this._collect(t,r,n),onPasteCells:(t,r,n,a)=>{const{copyType:i=B.COPY_TYPE.COPY,pasteType:s}=a,{range:d}=t||{},{range:l,unitId:c,subUnitId:u}=r;return this._generateMutations(l,{copyType:i,pasteType:s,copyRange:d,unitId:c,subUnitId:u})}})}_collect(t,r,n){const a=new o.ObjectMatrix;this._copyInfo={unitId:t,subUnitId:r,matrix:a};const i=this._injector.invoke(l=>$.rangeToDiscreteRange(n,l,t,r));if(!i)return;const{rows:s,cols:d}=i;s.forEach((l,c)=>{d.forEach((u,p)=>{const h=this._sheetDataValidationModel.getRuleIdByLocation(t,r,l,u);a.setValue(c,p,h!=null?h:"")})})}_generateMutations(t,r){if(!this._copyInfo)return{redos:[],undos:[]};if(r.copyType===B.COPY_TYPE.CUT)return this._copyInfo=null,{redos:[],undos:[]};if(!this._copyInfo||!this._copyInfo.matrix.getSizeOf()||!r.copyRange)return{redos:[],undos:[]};if([B.PREDEFINED_HOOK_NAME.SPECIAL_PASTE_COL_WIDTH,B.PREDEFINED_HOOK_NAME.SPECIAL_PASTE_VALUE,B.PREDEFINED_HOOK_NAME.SPECIAL_PASTE_FORMAT,B.PREDEFINED_HOOK_NAME.SPECIAL_PASTE_FORMULA].includes(r.pasteType))return{redos:[],undos:[]};const{unitId:a,subUnitId:i}=this._copyInfo;if(r.unitId!==a||i!==r.subUnitId){const s=this._sheetDataValidationModel.getRuleObjectMatrix(r.unitId,r.subUnitId).clone(),d=new o.ObjectMatrix,l=new Set,{ranges:[c,u],mapFunc:p}=B.virtualizeDiscreteRanges([r.copyRange,t]),h=B.getRepeatRange(c,u,!0),m=new Map;h.forEach(({startRange:f})=>{var y;(y=this._copyInfo)==null||y.matrix.forValue((_,D,w)=>{const C=o.Rectangle.getPositionRange({startRow:_,endRow:_,startColumn:D,endColumn:D},f),E=`${i}-${w}`,W=this._sheetDataValidationModel.getRuleById(a,i,w);!this._sheetDataValidationModel.getRuleById(r.unitId,r.subUnitId,E)&&W&&m.set(E,{...W,uid:E});const{row:H,col:P}=p(C.startRow,C.startColumn);l.add(E),d.setValue(H,P,E)})});const g=Array.from(l).map(f=>({id:f,ranges:o.queryObjectMatrix(d,y=>y===f)}));s.addRangeRules(g);const{redoMutations:O,undoMutations:M}=S.getDataValidationDiffMutations(r.unitId,r.subUnitId,s.diffWithAddition(this._sheetDataValidationModel.getRules(r.unitId,r.subUnitId),m.values()),this._injector,"patched",!1);return{redos:O,undos:M}}else{const s=this._sheetDataValidationModel.getRuleObjectMatrix(a,i).clone(),d=new o.ObjectMatrix,l=new Set,{ranges:[c,u],mapFunc:p}=B.virtualizeDiscreteRanges([r.copyRange,t]);B.getRepeatRange(c,u,!0).forEach(({startRange:M})=>{var f;(f=this._copyInfo)==null||f.matrix.forValue((y,_,D)=>{const w=o.Rectangle.getPositionRange({startRow:y,endRow:y,startColumn:_,endColumn:_},M),{row:C,col:E}=p(w.startRow,w.startColumn);d.setValue(C,E,D),l.add(D)})});const m=Array.from(l).map(M=>({id:M,ranges:o.queryObjectMatrix(d,f=>f===M)}));s.addRangeRules(m);const{redoMutations:g,undoMutations:O}=S.getDataValidationDiffMutations(a,i,s.diff(this._sheetDataValidationModel.getRules(a,i)),this._injector,"patched",!1);return{redos:g,undos:O}}}};Me=Ht([Je(0,B.ISheetClipboardService),Je(1,o.Inject(S.SheetDataValidationModel)),Je(2,o.Inject(o.Injector))],Me);var $t=Object.getOwnPropertyDescriptor,Yt=(e,t,r,n)=>{for(var a=n>1?void 0:n?$t(t,r):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(a=s(a)||a);return a},Qe=(e,t)=>(r,n)=>t(r,n,e);let be=class extends o.Disposable{constructor(e,t,r){super(),this._localeService=e,this._commandService=t,this._sheetPermissionCheckController=r,this._commandExecutedListener()}_commandExecutedListener(){this.disposeWithMe(this._commandService.beforeCommandExecuted(e=>{e.id===S.AddSheetDataValidationCommand.id&&(this._sheetPermissionCheckController.permissionCheckWithRanges({workbookTypes:[$.WorkbookEditablePermission],rangeTypes:[$.RangeProtectionPermissionEditPoint],worksheetTypes:[$.WorksheetEditPermission,$.WorksheetSetCellStylePermission]})||this._sheetPermissionCheckController.blockExecuteWithoutPermission(this._localeService.t("permission.dialog.setStyleErr"))),e.id===S.UpdateSheetDataValidationRangeCommand.id&&(this._sheetPermissionCheckController.permissionCheckWithRanges({workbookTypes:[$.WorkbookEditablePermission],rangeTypes:[$.RangeProtectionPermissionEditPoint],worksheetTypes:[$.WorksheetEditPermission,$.WorksheetSetCellStylePermission]},e.params.ranges)||this._sheetPermissionCheckController.blockExecuteWithoutPermission(this._localeService.t("permission.dialog.setStyleErr")))}))}};be=Yt([Qe(0,o.Inject(o.LocaleService)),Qe(1,o.ICommandService),Qe(2,o.Inject($.SheetPermissionCheckController))],be);const pt="sheet.menu.data-validation";function Xt(e){return{id:pt,type:V.MenuItemType.SUBITEMS,icon:"DataValidationIcon",tooltip:"dataValidation.title",hidden$:V.getMenuHiddenObservable(e,o.UniverInstanceType.UNIVER_SHEET),disabled$:B.getCurrentRangeDisable$(e,{workbookTypes:[$.WorkbookEditablePermission],worksheetTypes:[$.WorksheetSetCellStylePermission,$.WorksheetEditPermission],rangeTypes:[$.RangeProtectionPermissionEditPoint]})}}function zt(e){return{id:me.id,title:"dataValidation.panel.title",type:V.MenuItemType.BUTTON}}function Kt(e){return{id:ke.id,title:"dataValidation.panel.add",type:V.MenuItemType.BUTTON}}const Zt={[V.RibbonDataGroup.RULES]:{[pt]:{order:0,menuItemFactory:Xt,[me.id]:{order:0,menuItemFactory:zt},[ke.id]:{order:1,menuItemFactory:Kt}}}};var Gt=Object.getOwnPropertyDescriptor,gt=(e,t,r,n)=>{for(var a=n>1?void 0:n?Gt(t,r):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(a=s(a)||a);return a},Z=(e,t)=>(r,n)=>t(r,n,e);const ft={tr:{size:6,color:"#fe4b4b"}};let Ve=class extends o.RxDisposable{constructor(e,t,r,n,a,i,s,d,l,c,u){super(),this._commandService=e,this._menuManagerService=t,this._renderManagerService=r,this._univerInstanceService=n,this._autoHeightController=a,this._dropdownManagerService=i,this._sheetDataValidationModel=s,this._dataValidatorRegistryService=d,this._sheetInterceptorService=l,this._dataValidationCacheService=c,this._editorBridgeService=u,this._initMenu(),this._initDropdown(),this._initViewModelIntercept(),this._initAutoHeight()}_initMenu(){this._menuManagerService.mergeMenu(Zt)}_initDropdown(){this._editorBridgeService&&this.disposeWithMe(this._editorBridgeService.visible$.subscribe(e=>{var r;if(!e.visible){((r=this._dropdownManagerService.activeDropdown)==null?void 0:r.trigger)==="editor-bridge"&&this._dropdownManagerService.hideDropdown();return}const t=this._editorBridgeService.getEditCellState();if(t){const{unitId:n,sheetId:a,row:i,column:s}=t,d=this._univerInstanceService.getUniverSheetInstance(n);if(!d)return;const l=this._sheetDataValidationModel.getRuleByLocation(n,a,i,s);if(!l)return;const c=this._dataValidatorRegistryService.getValidatorItem(l.type);if(!(c!=null&&c.dropdownType))return;const u=d.getActiveSheet();if(!u)return;const p=this._dropdownManagerService.activeDropdown,h=p==null?void 0:p.location;if(h&&h.unitId===n&&h.subUnitId===a&&h.row===i&&h.col===s)return;this._dropdownManagerService.showDropdown({location:{unitId:n,subUnitId:a,row:i,col:s,workbook:d,worksheet:u},trigger:"editor-bridge",closeOnOutSide:!1})}}))}_initViewModelIntercept(){this.disposeWithMe(this._sheetInterceptorService.intercept($.INTERCEPTOR_POINT.CELL_CONTENT,{effect:o.InterceptorEffectEnum.Style,priority:$.InterceptCellContentPriority.DATA_VALIDATION,handler:(e,t,r)=>{var f,y,_,D,w;const{row:n,col:a,unitId:i,subUnitId:s,workbook:d,worksheet:l}=t,c=this._sheetDataValidationModel.getRuleIdByLocation(i,s,n,a);if(!c)return r(e);const u=this._sheetDataValidationModel.getRuleById(i,s,c);if(!u)return r(e);const p=(f=this._dataValidationCacheService.getValue(i,s,n,a))!=null?f:o.DataValidationStatus.VALID,h=this._dataValidatorRegistryService.getValidatorItem(u.type),m=t.rawData;let g;const O={get value(){var C;return g!==void 0||(g=(C=S.getCellValueOrigin(m))!=null?C:null),g}},M={get value(){var C;return`${(C=O.value)!=null?C:""}`}};return(!e||e===t.rawData)&&(e={...t.rawData}),e.markers={...e==null?void 0:e.markers,...p===o.DataValidationStatus.INVALID?ft:null},e.customRender=[...(y=e==null?void 0:e.customRender)!=null?y:[],...h!=null&&h.canvasRender?[h.canvasRender]:[]],e.fontRenderExtension={...e==null?void 0:e.fontRenderExtension,isSkip:((_=e==null?void 0:e.fontRenderExtension)==null?void 0:_.isSkip)||((D=h==null?void 0:h.skipDefaultFontRender)==null?void 0:D.call(h,u,O.value,t))},e.interceptorStyle={...e==null?void 0:e.interceptorStyle,...h==null?void 0:h.getExtraStyle(u,M.value,{get style(){const C=d.getStyles();return(typeof(e==null?void 0:e.s)=="string"?C.get(e==null?void 0:e.s):e==null?void 0:e.s)||{}}},n,a)},e.interceptorAutoHeight=()=>{var H,P,k,F,N,x;const C=(P=(H=this._renderManagerService.getRenderById(i))==null?void 0:H.with(B.SheetSkeletonManagerService).getSkeletonParam(s))==null?void 0:P.skeleton;if(!C)return;const E=C.worksheet.getMergedCell(n,a),W={data:e,style:C.getStyles().getStyleByCell(e),primaryWithCoord:C.getCellWithCoordByIndex((k=E==null?void 0:E.startRow)!=null?k:n,(F=E==null?void 0:E.startColumn)!=null?F:a),unitId:i,subUnitId:s,row:n,col:a,workbook:d,worksheet:l};return(x=(N=h==null?void 0:h.canvasRender)==null?void 0:N.calcCellAutoHeight)==null?void 0:x.call(N,W)},e.interceptorAutoWidth=()=>{var H,P,k,F,N,x;const C=(P=(H=this._renderManagerService.getRenderById(i))==null?void 0:H.with(B.SheetSkeletonManagerService).getSkeletonParam(s))==null?void 0:P.skeleton;if(!C)return;const E=C.worksheet.getMergedCell(n,a),W={data:e,style:C.getStyles().getStyleByCell(e),primaryWithCoord:C.getCellWithCoordByIndex((k=E==null?void 0:E.startRow)!=null?k:n,(F=E==null?void 0:E.startColumn)!=null?F:a),unitId:i,subUnitId:s,row:n,col:a,workbook:d,worksheet:l};return(x=(N=h==null?void 0:h.canvasRender)==null?void 0:N.calcCellAutoWidth)==null?void 0:x.call(N,W)},e.coverable=((w=e==null?void 0:e.coverable)!=null?w:!0)&&!(u.type===o.DataValidationType.LIST||u.type===o.DataValidationType.LIST_MULTIPLE),r(e)}}))}_initAutoHeight(){this._sheetDataValidationModel.ruleChange$.pipe(ie.filter(e=>e.source==="command"),ie.bufferTime(100)).subscribe(e=>{if(e.length===0)return;const t=[];if(e.forEach(r=>{var n;(r.rule.type===o.DataValidationType.LIST_MULTIPLE||r.rule.type===o.DataValidationType.LIST)&&(n=r.rule)!=null&&n.ranges&&t.push(...r.rule.ranges)}),t.length){const r=this._autoHeightController.getUndoRedoParamsOfAutoHeight(t);o.sequenceExecute(r.redos,this._commandService)}})}};Ve=gt([Z(0,o.ICommandService),Z(1,V.IMenuManagerService),Z(2,T.IRenderManagerService),Z(3,o.IUniverInstanceService),Z(4,o.Inject(B.AutoHeightController)),Z(5,o.Inject(fe)),Z(6,o.Inject(S.SheetDataValidationModel)),Z(7,o.Inject(X.DataValidatorRegistryService)),Z(8,o.Inject($.SheetInterceptorService)),Z(9,o.Inject(S.DataValidationCacheService)),Z(10,o.Optional(B.IEditorBridgeService))],Ve);let mt=class extends o.RxDisposable{constructor(e,t,r,n,a,i,s){super(),this._commandService=e,this._renderManagerService=t,this._autoHeightController=r,this._dataValidatorRegistryService=n,this._sheetInterceptorService=a,this._sheetDataValidationModel=i,this._dataValidationCacheService=s,this._initViewModelIntercept(),this._initAutoHeight()}_initViewModelIntercept(){this.disposeWithMe(this._sheetInterceptorService.intercept($.INTERCEPTOR_POINT.CELL_CONTENT,{effect:o.InterceptorEffectEnum.Style,priority:$.InterceptCellContentPriority.DATA_VALIDATION,handler:(e,t,r)=>{var M,f,y,_,D;const{row:n,col:a,unitId:i,subUnitId:s,workbook:d,worksheet:l}=t,c=this._sheetDataValidationModel.getRuleIdByLocation(i,s,n,a);if(!c)return r(e);const u=this._sheetDataValidationModel.getRuleById(i,s,c);if(!u)return r(e);const p=(M=this._dataValidationCacheService.getValue(i,s,n,a))!=null?M:o.DataValidationStatus.VALID,h=this._dataValidatorRegistryService.getValidatorItem(u.type),m=l.getCellRaw(n,a),g=S.getCellValueOrigin(m),O=`${g!=null?g:""}`;return(!e||e===t.rawData)&&(e={...t.rawData}),e.markers={...e==null?void 0:e.markers,...p===o.DataValidationStatus.INVALID?ft:null},e.customRender=[...(f=e==null?void 0:e.customRender)!=null?f:[],...h!=null&&h.canvasRender?[h.canvasRender]:[]],e.fontRenderExtension={...e==null?void 0:e.fontRenderExtension,isSkip:((y=e==null?void 0:e.fontRenderExtension)==null?void 0:y.isSkip)||((_=h==null?void 0:h.skipDefaultFontRender)==null?void 0:_.call(h,u,g,t))},e.interceptorStyle={...e==null?void 0:e.interceptorStyle,...h==null?void 0:h.getExtraStyle(u,O,{get style(){const w=d.getStyles();return(typeof(e==null?void 0:e.s)=="string"?w.get(e==null?void 0:e.s):e==null?void 0:e.s)||{}}},n,a)},e.interceptorAutoHeight=()=>{var W,H,P,k,F,N;const w=(H=(W=this._renderManagerService.getRenderById(i))==null?void 0:W.with(B.SheetSkeletonManagerService).getSkeletonParam(s))==null?void 0:H.skeleton;if(!w)return;const C=w.worksheet.getMergedCell(n,a),E={data:e,style:w.getStyles().getStyleByCell(e),primaryWithCoord:w.getCellWithCoordByIndex((P=C==null?void 0:C.startRow)!=null?P:n,(k=C==null?void 0:C.startColumn)!=null?k:a),unitId:i,subUnitId:s,row:n,col:a,workbook:d,worksheet:l};return(N=(F=h==null?void 0:h.canvasRender)==null?void 0:F.calcCellAutoHeight)==null?void 0:N.call(F,E)},e.interceptorAutoWidth=()=>{var W,H,P,k,F,N;const w=(H=(W=this._renderManagerService.getRenderById(i))==null?void 0:W.with(B.SheetSkeletonManagerService).getSkeletonParam(s))==null?void 0:H.skeleton;if(!w)return;const C=w.worksheet.getMergedCell(n,a),E={data:e,style:w.getStyles().getStyleByCell(e),primaryWithCoord:w.getCellWithCoordByIndex((P=C==null?void 0:C.startRow)!=null?P:n,(k=C==null?void 0:C.startColumn)!=null?k:a),unitId:i,subUnitId:s,row:n,col:a,workbook:d,worksheet:l};return(N=(F=h==null?void 0:h.canvasRender)==null?void 0:F.calcCellAutoWidth)==null?void 0:N.call(F,E)},e.coverable=((D=e==null?void 0:e.coverable)!=null?D:!0)&&!(u.type===o.DataValidationType.LIST||u.type===o.DataValidationType.LIST_MULTIPLE),r(e)}}))}_initAutoHeight(){this._sheetDataValidationModel.ruleChange$.pipe(ie.filter(e=>e.source==="command"),ie.bufferTime(16)).subscribe(e=>{const t=[];if(e.forEach(r=>{var n;(r.rule.type===o.DataValidationType.LIST_MULTIPLE||r.rule.type===o.DataValidationType.LIST)&&(n=r.rule)!=null&&n.ranges&&t.push(...r.rule.ranges)}),t.length){const r=this._autoHeightController.getUndoRedoParamsOfAutoHeight(t);o.sequenceExecute(r.redos,this._commandService)}})}};mt=gt([Z(0,o.ICommandService),Z(1,T.IRenderManagerService),Z(2,o.Inject(B.AutoHeightController)),Z(3,o.Inject(X.DataValidatorRegistryService)),Z(4,o.Inject($.SheetInterceptorService)),Z(5,o.Inject(S.SheetDataValidationModel)),Z(6,o.Inject(S.DataValidationCacheService))],mt);var qt=Object.getOwnPropertyDescriptor,Jt=(e,t,r,n)=>{for(var a=n>1?void 0:n?qt(t,r):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(a=s(a)||a);return a},St=(e,t)=>(r,n)=>t(r,n,e);let Ne=class extends o.Disposable{constructor(e,t,r){super(),this._context=e,this._sheetDataValidationModel=t,this._sheetSkeletonManagerService=r,this._initSkeletonChange()}_initSkeletonChange(){const e=t=>{var n;if(!t.length)return;const r=new Set;t.forEach(a=>{r.add(a.subUnitId)}),r.forEach(a=>{var i;(i=this._sheetSkeletonManagerService.getSkeletonParam(a))==null||i.skeleton.makeDirty(!0)}),(n=this._context.mainComponent)==null||n.makeForceDirty()};this.disposeWithMe(this._sheetDataValidationModel.validStatusChange$.pipe(o.bufferDebounceTime(16)).subscribe(e))}};Ne=Jt([St(1,o.Inject(S.SheetDataValidationModel)),St(2,o.Inject(B.SheetSkeletonManagerService))],Ne);var oe=function(){return oe=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++){t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},oe.apply(this,arguments)},Qt=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)t.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r},_e=R.forwardRef(function(e,t){var r=e.icon,n=e.id,a=e.className,i=e.extend,s=Qt(e,["icon","id","className","extend"]),d="univerjs-icon univerjs-icon-".concat(n," ").concat(a||"").trim(),l=R.useRef("_".concat(nn()));return _t(r,"".concat(n),{defIds:r.defIds,idSuffix:l.current},oe({ref:t,className:d},s),i)});function _t(e,t,r,n,a){return R.createElement(e.tag,oe(oe({key:t},en(e,r,a)),n),(tn(e,r).children||[]).map(function(i,s){return _t(i,"".concat(t,"-").concat(e.tag,"-").concat(s),r,void 0,a)}))}function en(e,t,r){var n=oe({},e.attrs);r!=null&&r.colorChannel1&&n.fill==="colorChannel1"&&(n.fill=r.colorChannel1),e.tag==="mask"&&n.id&&(n.id=n.id+t.idSuffix),Object.entries(n).forEach(function(i){var s=i[0],d=i[1];s==="mask"&&typeof d=="string"&&(n[s]=d.replace(/url\(#(.*)\)/,"url(#$1".concat(t.idSuffix,")")))});var a=t.defIds;return!a||a.length===0||(e.tag==="use"&&n["xlink:href"]&&(n["xlink:href"]=n["xlink:href"]+t.idSuffix),Object.entries(n).forEach(function(i){var s=i[0],d=i[1];typeof d=="string"&&(n[s]=d.replace(/url\(#(.*)\)/,"url(#$1".concat(t.idSuffix,")")))})),n}function tn(e,t){var r,n=t.defIds;return!n||n.length===0?e:e.tag==="defs"&&(!((r=e.children)===null||r===void 0)&&r.length)?oe(oe({},e),{children:e.children.map(function(a){return typeof a.attrs.id=="string"&&n&&n.includes(a.attrs.id)?oe(oe({},a),{attrs:oe(oe({},a.attrs),{id:a.attrs.id+t.idSuffix})}):a})}):e}function nn(){return Math.random().toString(36).substring(2,8)}_e.displayName="UniverIcon";var rn={tag:"svg",attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 17 16",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M1.4917 3.07803C1.4917 2.19437 2.20804 1.47803 3.0917 1.47803H5.6917C6.57536 1.47803 7.2917 2.19437 7.2917 3.07803V5.67803C7.2917 6.56168 6.57535 7.27803 5.6917 7.27803H3.0917C2.20804 7.27803 1.4917 6.56168 1.4917 5.67803V3.07803ZM3.0917 2.67803C2.87078 2.67803 2.6917 2.85711 2.6917 3.07803V5.67803C2.6917 5.89894 2.87079 6.07803 3.0917 6.07803H5.6917C5.91261 6.07803 6.0917 5.89894 6.0917 5.67803V3.07803C6.0917 2.85711 5.91261 2.67803 5.6917 2.67803H3.0917Z",fillRule:"evenodd",clipRule:"evenodd"}},{tag:"path",attrs:{fill:"currentColor",d:"M14.6175 2.45279C14.8518 2.68711 14.8518 3.06701 14.6175 3.30132L11.6151 6.30365C11.3957 6.52307 11.0451 6.53897 10.8067 6.34031L8.80915 4.67566C8.55458 4.46352 8.52019 4.08518 8.73233 3.83062C8.94447 3.57605 9.32281 3.54166 9.57737 3.7538L11.154 5.06767L13.769 2.45278C14.0033 2.21847 14.3832 2.21848 14.6175 2.45279Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M14.1175 9.19746C14.3518 9.43178 14.3518 9.81168 14.1175 10.046L12.5418 11.6217L14.1175 13.1975C14.3518 13.4318 14.3518 13.8117 14.1175 14.046C13.8832 14.2803 13.5033 14.2803 13.269 14.046L11.6933 12.4703L10.1175 14.046C9.88321 14.2803 9.50331 14.2803 9.269 14.046C9.03468 13.8117 9.03468 13.4318 9.269 13.1975L10.8447 11.6217L9.269 10.046C9.03468 9.81168 9.03468 9.43178 9.269 9.19746C9.50331 8.96315 9.88321 8.96315 10.1175 9.19746L11.6933 10.7732L13.269 9.19746C13.5033 8.96315 13.8832 8.96315 14.1175 9.19746Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M3.0917 8.72168C2.20804 8.72168 1.4917 9.43802 1.4917 10.3217V12.9217C1.4917 13.8053 2.20804 14.5217 3.0917 14.5217H5.6917C6.57535 14.5217 7.2917 13.8053 7.2917 12.9217V10.3217C7.2917 9.43802 6.57536 8.72168 5.6917 8.72168H3.0917ZM2.6917 10.3217C2.6917 10.1008 2.87078 9.92168 3.0917 9.92168H5.6917C5.91261 9.92168 6.0917 10.1008 6.0917 10.3217V12.9217C6.0917 13.1426 5.91261 13.3217 5.6917 13.3217H3.0917C2.87079 13.3217 2.6917 13.1426 2.6917 12.9217V10.3217Z",fillRule:"evenodd",clipRule:"evenodd"}}]},It=R.forwardRef(function(e,t){return R.createElement(_e,Object.assign({},e,{id:"data-validation-icon",ref:t,icon:rn}))});It.displayName="DataValidationIcon";var an={tag:"svg",attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 16",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M5.3313 1.4667C5.3313 1.13533 5.59993 0.866699 5.9313 0.866699H10.069C10.4004 0.866699 10.669 1.13533 10.669 1.4667C10.669 1.79807 10.4004 2.0667 10.069 2.0667H5.9313C5.59993 2.0667 5.3313 1.79807 5.3313 1.4667Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M1.09985 3.64443C1.09985 3.31306 1.36848 3.04443 1.69985 3.04443H14.2999C14.6312 3.04443 14.8999 3.31306 14.8999 3.64443C14.8999 3.9758 14.6312 4.24443 14.2999 4.24443H1.69985C1.36848 4.24443 1.09985 3.9758 1.09985 3.64443Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M6.12398 8.30171C6.35829 8.0674 6.73819 8.0674 6.97251 8.30171L8.00007 9.32928L9.02764 8.30171C9.26195 8.0674 9.64185 8.0674 9.87617 8.30171C10.1105 8.53603 10.1105 8.91593 9.87617 9.15024L8.8486 10.1778L9.87617 11.2054C10.1105 11.4397 10.1105 11.8196 9.87617 12.0539C9.64185 12.2882 9.26195 12.2882 9.02764 12.0539L8.00007 11.0263L6.97251 12.0539C6.73819 12.2882 6.35829 12.2882 6.12398 12.0539C5.88966 11.8196 5.88966 11.4397 6.12398 11.2054L7.15154 10.1778L6.12398 9.15024C5.88966 8.91593 5.88966 8.53603 6.12398 8.30171Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M4.75332 5.22217C3.86966 5.22217 3.15332 5.93851 3.15332 6.82217V12.5331C3.15332 13.9691 4.31738 15.1332 5.75332 15.1332H10.2465C11.6825 15.1332 12.8465 13.9691 12.8465 12.5331V6.82217C12.8465 5.93851 12.1302 5.22217 11.2465 5.22217H4.75332ZM4.35332 6.82217C4.35332 6.60125 4.53241 6.42217 4.75332 6.42217H11.2465C11.4674 6.42217 11.6465 6.60125 11.6465 6.82217V12.5331C11.6465 13.3063 11.0197 13.9332 10.2465 13.9332H5.75332C4.98012 13.9332 4.35332 13.3063 4.35332 12.5331V6.82217Z",fillRule:"evenodd",clipRule:"evenodd"}}]},et=R.forwardRef(function(e,t){return R.createElement(_e,Object.assign({},e,{id:"delete-icon",ref:t,icon:an}))});et.displayName="DeleteIcon";var on={tag:"svg",attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 16",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M8.6 1.99991C8.60001 1.66854 8.33138 1.39991 8.00001 1.3999C7.66864 1.3999 7.40001 1.66853 7.4 1.9999L7.39996 7.3999H1.9999C1.66853 7.3999 1.3999 7.66853 1.3999 7.9999C1.3999 8.33127 1.66853 8.5999 1.9999 8.5999H7.39995L7.3999 13.9999C7.3999 14.3313 7.66853 14.5999 7.9999 14.5999C8.33127 14.5999 8.5999 14.3313 8.5999 13.9999L8.59995 8.5999H13.9999C14.3313 8.5999 14.5999 8.33127 14.5999 7.9999C14.5999 7.66853 14.3313 7.3999 13.9999 7.3999H8.59996L8.6 1.99991Z"}}]},Ct=R.forwardRef(function(e,t){return R.createElement(_e,Object.assign({},e,{id:"increase-icon",ref:t,icon:on}))});Ct.displayName="IncreaseIcon";var sn={tag:"svg",attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 16",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11.3536 6.14645C11.5488 6.34171 11.5488 6.65829 11.3536 6.85355L8.35355 9.85355C8.15829 10.0488 7.84171 10.0488 7.64645 9.85355L4.64645 6.85355C4.45118 6.65829 4.45118 6.34171 4.64645 6.14645C4.84171 5.95118 5.15829 5.95118 5.35355 6.14645L8 8.79289L10.6464 6.14645C10.8417 5.95118 11.1583 5.95118 11.3536 6.14645Z",fillRule:"evenodd",clipRule:"evenodd"}}]},tt=R.forwardRef(function(e,t){return R.createElement(_e,Object.assign({},e,{id:"more-down-icon",ref:t,icon:sn}))});tt.displayName="MoreDownIcon";var ln={tag:"svg",attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 16",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M4.64645 9.85355C4.45118 9.65829 4.45118 9.34171 4.64645 9.14645L7.64645 6.14645C7.84171 5.95118 8.15829 5.95118 8.35355 6.14645L11.3536 9.14645C11.5488 9.34171 11.5488 9.65829 11.3536 9.85355C11.1583 10.0488 10.8417 10.0488 10.6464 9.85355L8 7.20711L5.35355 9.85355C5.15829 10.0488 4.84171 10.0488 4.64645 9.85355Z",fillRule:"evenodd",clipRule:"evenodd"}}]},yt=R.forwardRef(function(e,t){return R.createElement(_e,Object.assign({},e,{id:"more-up-icon",ref:t,icon:ln}))});yt.displayName="MoreUpIcon";var dn={tag:"svg",attrs:{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 16",width:"1em",height:"1em"},children:[{tag:"mask",attrs:{id:"mask0_622_8",width:16,height:16,x:0,y:0,maskUnits:"userSpaceOnUse"},children:[{tag:"path",attrs:{fill:"#D9D9D9",d:"M0 0H16V16H0z"}}]},{tag:"g",attrs:{fill:"currentColor",mask:"url(#mask0_622_8)"},children:[{tag:"path",attrs:{d:"M6 5C6.55228 5 7 4.55228 7 4C7 3.44772 6.55228 3 6 3C5.44772 3 5 3.44772 5 4C5 4.55228 5.44772 5 6 5Z"}},{tag:"path",attrs:{d:"M6 9C6.55228 9 7 8.55229 7 8C7 7.44772 6.55228 7 6 7C5.44772 7 5 7.44772 5 8C5 8.55229 5.44772 9 6 9Z"}},{tag:"path",attrs:{d:"M7 12C7 12.5523 6.55228 13 6 13C5.44772 13 5 12.5523 5 12C5 11.4477 5.44772 11 6 11C6.55228 11 7 11.4477 7 12Z"}},{tag:"path",attrs:{d:"M10 5C10.5523 5 11 4.55228 11 4C11 3.44772 10.5523 3 10 3C9.44771 3 9 3.44772 9 4C9 4.55228 9.44771 5 10 5Z"}},{tag:"path",attrs:{d:"M11 8C11 8.55229 10.5523 9 10 9C9.44771 9 9 8.55229 9 8C9 7.44772 9.44771 7 10 7C10.5523 7 11 7.44772 11 8Z"}},{tag:"path",attrs:{d:"M10 13C10.5523 13 11 12.5523 11 12C11 11.4477 10.5523 11 10 11C9.44771 11 9 11.4477 9 12C9 12.5523 9.44771 13 10 13Z"}}]}]},wt=R.forwardRef(function(e,t){return R.createElement(_e,Object.assign({},e,{id:"sequence-icon",ref:t,icon:dn}))});wt.displayName="SequenceIcon";function cn(e){var c;const t=V.useDependency(o.LocaleService),r=V.useDependency(V.ComponentManager),{value:n,onChange:a,extraComponent:i}=e,[s,d]=R.useState(!1),l=i?r.get(i):null;return v.jsxs(v.Fragment,{children:[v.jsxs("div",{className:"univer-mb-3 univer-flex univer-cursor-pointer univer-items-center univer-text-sm univer-text-gray-900 dark:!univer-text-white",onClick:()=>d(!s),children:[t.t("dataValidation.panel.options"),s?v.jsx(yt,{className:"univer-ml-1"}):v.jsx(tt,{className:"univer-ml-1"})]}),s&&v.jsxs(v.Fragment,{children:[l?v.jsx(l,{value:n,onChange:a}):null,v.jsx(b.FormLayout,{label:t.t("dataValidation.panel.invalid"),children:v.jsxs(b.RadioGroup,{value:`${(c=n.errorStyle)!=null?c:o.DataValidationErrorStyle.WARNING}`,onChange:u=>a({...n,errorStyle:+u}),children:[v.jsx(b.Radio,{value:`${o.DataValidationErrorStyle.WARNING}`,children:t.t("dataValidation.panel.showWarning")}),v.jsx(b.Radio,{value:`${o.DataValidationErrorStyle.STOP}`,children:t.t("dataValidation.panel.rejectInput")})]})}),v.jsx(b.FormLayout,{label:t.t("dataValidation.panel.messageInfo"),children:v.jsx(b.Checkbox,{checked:n.showErrorMessage,onChange:()=>a({...n,showErrorMessage:!n.showErrorMessage}),children:t.t("dataValidation.panel.showInfo")})}),n.showErrorMessage?v.jsx(b.FormLayout,{children:v.jsx(b.Input,{value:n.error,onChange:u=>a({...n,error:u})})}):null]})]})}const un=e=>o.debounce(async(t,r,n,a)=>{const i=await e.executeCommand(t,r,n);a==null||a(i)},1e3);function hn(e,t,r){var n,a,i,s;return t?((a=(n=e.getUnit(t))==null?void 0:n.getSheetBySheetName(r))==null?void 0:a.getSheetId())||"":((s=(i=e.getCurrentUnitForType(o.UniverInstanceType.UNIVER_SHEET))==null?void 0:i.getSheetBySheetName(r))==null?void 0:s.getSheetId())||""}function vn(){var L,Y;const[e,t]=R.useState(0),r=V.useDependency(ue),n=V.useObservable(r.activeRule$,r.activeRule),{unitId:a,subUnitId:i,rule:s}=n||{},d=s.uid,l=V.useDependency(X.DataValidatorRegistryService),c=V.useDependency(o.IUniverInstanceService),u=V.useDependency(V.ComponentManager),p=V.useDependency(o.ICommandService),h=V.useDependency(X.DataValidationModel),m=V.useDependency(o.LocaleService),[g,O]=R.useState(s),M=l.getValidatorItem(g.type),[f,y]=R.useState(!1),_=l.getValidatorsByScope(X.DataValidatorRegistryScope.SHEET),[D,w]=R.useState(()=>g.ranges.map(I=>({unitId:"",sheetId:"",range:I}))),C=R.useMemo(()=>un(p),[p]),[E,W]=R.useState(!1),[H,P]=R.useState(!1),k=R.useRef(null),F=V.useDependency($.SheetsSelectionsService);if(R.useEffect(()=>()=>{const I=F.getCurrentLastSelection();I&&F.setSelections([I])},[F]),R.useEffect(()=>{p.onCommandExecuted(I=>{(I.id===o.UndoCommand.id||I.id===o.RedoCommand.id)&&setTimeout(()=>{const j=h.getRuleById(a,i,d);t(z=>z+1),j&&(O(j),w(j.ranges.map(z=>({unitId:"",sheetId:"",range:z}))))},20)})},[p,h,d,i,a]),!M)return null;const N=M.operators,x=M.operatorNames,K=g.operator?X.TWO_FORMULA_OPERATOR_COUNT.includes(g.operator):!1,G=()=>{var I,j,z;(j=(I=k.current)==null?void 0:I.editor)!=null&&j.isFocus()&&ne((z=k.current)==null?void 0:z.getValue()),!(!g.ranges.length||E)&&(M.validatorFormula(g,a,i).success?r.setActiveRule(null):y(!0))},ne=V.useEvent(I=>{const j=I.split(",").filter(Boolean).map(Xe.deserializeRangeWithSheet).map(te=>{const Rt=te.sheetName;if(Rt){const dr=hn(c,te.unitId,Rt);return{...te,sheetId:dr}}return{...te,sheetId:""}});if(o.isUnitRangesEqual(j,D))return;w(j);const z=j.filter(te=>(!te.unitId||te.unitId===a)&&(!te.sheetId||te.sheetId===i)).map(te=>te.range);if(O({...g,ranges:z}),z.length===0)return;const pe={unitId:a,subUnitId:i,ruleId:d,ranges:z};C(S.UpdateSheetDataValidationRangeCommand.id,pe)}),re=I=>{if(o.shallowEqual(I,X.getRuleSetting(g)))return;O({...g,...I});const j={unitId:a,subUnitId:i,ruleId:d,setting:I};C(S.UpdateSheetDataValidationSettingCommand.id,j,void 0)},se=async()=>{await p.executeCommand(S.RemoveSheetDataValidationCommand.id,{ruleId:d,unitId:a,subUnitId:i}),r.setActiveRule(null)},ce={type:g.type,operator:g.operator,formula1:g.formula1,formula2:g.formula2,allowBlank:g.allowBlank},q=I=>{const j=l.getValidatorItem(I);if(!j)return;const z=j.operators,pe=h.getRuleById(a,i,d),te=I===(pe==null?void 0:pe.type)||I.includes("list")&&(pe!=null&&pe.type.includes("list"))?{...pe,type:I}:{...g,type:I,operator:z[0],formula1:void 0,formula2:void 0};O(te),p.executeCommand(S.UpdateSheetDataValidationSettingCommand.id,{unitId:a,subUnitId:i,ruleId:g.uid,setting:X.getRuleSetting(te)})},J=u.get(M.formulaInput),ve=R.useMemo(()=>D.map(I=>Xe.serializeRange(I.range)).join(","),[]),ee=X.getRuleOptions(g),Q=I=>{o.shallowEqual(I,X.getRuleOptions(g))||(O({...g,...I}),C(S.UpdateSheetDataValidationOptionsCommand.id,{unitId:a,subUnitId:i,ruleId:d,options:I}))},ae=N.length&&!g.operator;return v.jsxs("div",{"data-u-comp":"data-validation-detail",className:"univer-py-2",children:[v.jsx(b.FormLayout,{label:m.t("dataValidation.panel.range"),error:!g.ranges.length||E?m.t("dataValidation.panel.rangeError"):"",children:v.jsx(ze.RangeSelector,{selectorRef:k,unitId:a,subUnitId:i,initialValue:ve,onChange:(I,j)=>{var z;!H&&((z=k.current)!=null&&z.verify())&&ne(j)},onFocusChange:(I,j)=>{var z;P(I),!I&&j&&((z=k.current)!=null&&z.verify())&&ne(j)},onVerify:I=>W(!I)})}),v.jsx(b.FormLayout,{label:m.t("dataValidation.panel.type"),children:v.jsx(b.Select,{className:"univer-w-full",value:g.type,options:(L=_==null?void 0:_.sort((I,j)=>I.order-j.order))==null?void 0:L.map(I=>({label:m.t(I.title),value:I.id})),onChange:q})}),N!=null&&N.length?v.jsx(b.FormLayout,{label:m.t("dataValidation.panel.operator"),children:v.jsx(b.Select,{className:"univer-w-full",value:`${g.operator}`,options:[{value:"",label:m.t("dataValidation.operators.legal")},...N.map((I,j)=>({value:`${I}`,label:x[j]}))],onChange:I=>{re({...ce,operator:I})}})}):null,J&&!ae?v.jsx(J,{isTwoFormula:K,value:{formula1:g.formula1,formula2:g.formula2},onChange:I=>{re({...ce,...I})},showError:f,validResult:M.validatorFormula(g,a,i),unitId:a,subUnitId:i,ruleId:d},e+g.type):null,v.jsx(b.FormLayout,{children:v.jsx(b.Checkbox,{checked:(Y=g.allowBlank)!=null?Y:!0,onChange:()=>{var I;return re({...ce,allowBlank:!((I=g.allowBlank)==null||I)})},children:m.t("dataValidation.panel.allowBlank")})}),v.jsx(cn,{value:ee,onChange:Q,extraComponent:M.optionsInput}),v.jsxs("div",{className:"univer-mt-5 univer-flex univer-flex-row univer-justify-end",children:[v.jsx(b.Button,{className:"univer-ml-3",onClick:se,children:m.t("dataValidation.panel.removeRule")}),v.jsx(b.Button,{className:"univer-ml-3",variant:"primary",onClick:G,children:m.t("dataValidation.panel.done")})]})]})}const pn=e=>{const{rule:t,onClick:r,unitId:n,subUnitId:a,disable:i}=e,s=V.useDependency(X.DataValidatorRegistryService),d=V.useDependency(o.ICommandService),l=V.useDependency(B.IMarkSelectionService),c=s.getValidatorItem(t.type),u=R.useRef(void 0),[p,h]=R.useState(!1),m=V.useDependency(o.ThemeService),g=V.useObservable(m.currentTheme$),O=R.useMemo(()=>{var w;const f=m.getColorFromTheme("primary.600"),y=m.getColorFromTheme("loop-color.2"),_=(w=m.getColorFromTheme(y))!=null?w:f,D=new o.ColorKit(_).toRgb();return{fill:`rgba(${D.r}, ${D.g}, ${D.b}, 0.1)`,stroke:_}},[g]),M=f=>{d.executeCommand(S.RemoveSheetDataValidationCommand.id,{ruleId:t.uid,unitId:n,subUnitId:a}),f.stopPropagation()};return R.useEffect(()=>()=>{var f;u.current&&((f=u.current)==null||f.forEach(y=>{y&&l.removeShape(y)}))},[l]),v.jsxs("div",{className:b.clsx(`
                  univer-bg-secondary univer-relative univer--ml-2 univer--mr-2 univer-box-border univer-flex
                  univer-w-[287px] univer-cursor-pointer univer-flex-col univer-justify-between univer-overflow-hidden
                  univer-rounded-md univer-p-2 univer-pr-9
                `,{"hover:univer-bg-gray-50 dark:hover:!univer-bg-gray-700":!i,"univer-opacity-50":i}),onClick:r,onMouseEnter:()=>{i||(h(!0),u.current=t.ranges.map(f=>l.addShape({range:f,style:O,primary:null})))},onMouseLeave:()=>{var f;h(!1),(f=u.current)==null||f.forEach(y=>{y&&l.removeShape(y)}),u.current=void 0},children:[v.jsx("div",{className:"univer-truncate univer-text-sm univer-font-medium univer-leading-[22px] univer-text-gray-900 dark:!univer-text-white",children:c==null?void 0:c.generateRuleName(t)}),v.jsx("div",{className:"univer-text-secondary univer-truncate univer-text-xs univer-leading-[18px] dark:!univer-text-gray-300",children:t.ranges.map(f=>Xe.serializeRange(f)).join(",")}),p?v.jsx("div",{className:"univer-absolute univer-right-2 univer-top-[19px] univer-flex univer-h-5 univer-w-5 univer-items-center univer-justify-center univer-rounded hover:univer-bg-gray-200 dark:!univer-text-gray-300 dark:hover:!univer-bg-gray-700",onClick:M,children:v.jsx(et,{})}):null]})};function gn(e){const t=V.useDependency(S.SheetDataValidationModel),r=V.useDependency(o.IUniverInstanceService),n=V.useDependency(o.ICommandService),a=V.useDependency(o.Injector),i=V.useDependency(ue),s=V.useDependency(o.LocaleService),[d,l]=R.useState([]),{workbook:c}=e,u=V.useObservable(c.activeSheet$,void 0,!0),p=c.getUnitId(),h=u==null?void 0:u.getSheetId();R.useEffect(()=>{l(t.getRules(p,h));const y=t.ruleChange$.subscribe(_=>{_.unitId===p&&_.subUnitId===h&&l(t.getRules(p,h))});return()=>{y.unsubscribe()}},[p,h,t]);const m=async()=>{const y=S.createDefaultNewRule(a),_={unitId:p,subUnitId:h,rule:y};await n.executeCommand(S.AddSheetDataValidationCommand.id,_),i.setActiveRule({unitId:p,subUnitId:h,rule:y})},g=()=>{n.executeCommand(S.RemoveSheetAllDataValidationCommand.id,{unitId:p,subUnitId:h})},M=(y=>{const _=r.getCurrentUnitForType(o.UniverInstanceType.UNIVER_SHEET),D=_.getActiveSheet(),w=_.getUnitId(),C=D.getSheetId();return y.map(W=>$.checkRangesEditablePermission(a,w,C,W.ranges)?{...W}:{...W,disable:!0})})(d),f=M==null?void 0:M.some(y=>y.disable);return v.jsxs("div",{className:"univer-pb-4",children:[M==null?void 0:M.map(y=>{var _;return v.jsx(pn,{unitId:p,subUnitId:h,onClick:()=>{y.disable||i.setActiveRule({unitId:p,subUnitId:h,rule:y})},rule:y,disable:(_=y.disable)!=null?_:!1},y.uid)}),v.jsxs("div",{className:"univer-mt-4 univer-flex univer-flex-row univer-justify-end univer-gap-2",children:[d.length&&!f?v.jsx(b.Button,{onClick:g,children:s.t("dataValidation.panel.removeAll")}):null,v.jsx(b.Button,{variant:"primary",onClick:m,children:s.t("dataValidation.panel.add")})]})]})}const fn=()=>{const e=V.useDependency(ue),t=V.useObservable(e.activeRule$,e.activeRule),r=V.useDependency(o.IUniverInstanceService),n=V.useObservable(()=>r.getCurrentTypeOfUnit$(o.UniverInstanceType.UNIVER_SHEET),void 0,void 0,[]),a=V.useObservable(()=>{var i;return(i=n==null?void 0:n.activeSheet$)!=null?i:ie.of(null)},void 0,void 0,[]);return!n||!a?null:t&&t.subUnitId===a.getSheetId()?v.jsx(vn,{},t.rule.uid):v.jsx(gn,{workbook:n})},mn=e=>{const{isTwoFormula:t=!1,value:r,onChange:n,showError:a,validResult:i}=e,s=V.useDependency(o.LocaleService),d=a?i==null?void 0:i.formula1:"",l=a?i==null?void 0:i.formula2:"";return t?v.jsxs(v.Fragment,{children:[v.jsx(b.FormLayout,{error:d,children:v.jsx(b.Input,{className:"univer-w-full",placeholder:s.t("dataValidation.panel.formulaPlaceholder"),value:r==null?void 0:r.formula1,onChange:c=>{n==null||n({...r,formula1:c})}})}),v.jsx("div",{className:"-univer-mt-2 univer-mb-1 univer-text-sm univer-text-gray-400",children:s.t("dataValidation.panel.formulaAnd")}),v.jsx(b.FormLayout,{error:l,children:v.jsx(b.Input,{className:"univer-w-full",placeholder:s.t("dataValidation.panel.formulaPlaceholder"),value:r==null?void 0:r.formula2,onChange:c=>{n==null||n({...r,formula2:c})}})})]}):v.jsx(b.FormLayout,{error:d,children:v.jsx(b.Input,{className:"univer-w-full",placeholder:s.t("dataValidation.panel.formulaPlaceholder"),value:r==null?void 0:r.formula1,onChange:c=>{n==null||n({formula1:c})}})})};function Sn(e){const{value:t,onChange:r,showError:n,validResult:a}=e,i=V.useDependency(o.LocaleService),s=n?a==null?void 0:a.formula1:"",d=n?a==null?void 0:a.formula2:"",[l,c]=R.useState(!((t==null?void 0:t.formula1)===void 0&&(t==null?void 0:t.formula2)===void 0));return v.jsxs(v.Fragment,{children:[v.jsx(b.FormLayout,{children:v.jsx(b.Checkbox,{checked:l,onChange:u=>{u?c(!0):(c(!1),r==null||r({...t,formula1:void 0,formula2:void 0}))},children:i.t("dataValidation.checkbox.tips")})}),l?v.jsx(b.FormLayout,{label:i.t("dataValidation.checkbox.checked"),error:s,children:v.jsx(b.Input,{className:"univer-w-full",placeholder:i.t("dataValidation.panel.valuePlaceholder"),value:t==null?void 0:t.formula1,onChange:u=>{r==null||r({...t,formula1:u||void 0})}})}):null,l?v.jsx(b.FormLayout,{label:i.t("dataValidation.checkbox.unchecked"),error:d,children:v.jsx(b.Input,{className:"univer-w-full",placeholder:i.t("dataValidation.panel.valuePlaceholder"),value:t==null?void 0:t.formula2,onChange:u=>{r==null||r({...t,formula2:u||void 0})}})}):null]})}function _n(e){var p;const{unitId:t,subUnitId:r,value:n,onChange:a,showError:i,validResult:s}=e,d=i?s==null?void 0:s.formula1:void 0,l=R.useRef(null),[c,u]=R.useState(!1);return V.useSidebarClick(h=>{var g;((g=l.current)==null?void 0:g.isClickOutSide(h))&&u(!1)}),v.jsx(b.FormLayout,{error:d,children:v.jsx(ze.FormulaEditor,{ref:l,className:b.clsx("univer-box-border univer-h-8 univer-w-full univer-cursor-pointer univer-items-center univer-rounded-lg univer-bg-white univer-pt-2 univer-transition-colors hover:univer-border-primary-600 dark:!univer-bg-gray-700 dark:!univer-text-white [&>div:first-child]:univer-px-2.5 [&>div]:univer-h-5 [&>div]:univer-ring-transparent",b.borderClassName),initValue:(p=n==null?void 0:n.formula1)!=null?p:"=",unitId:t,subUnitId:r,isFocus:c,isSupportAcrossSheet:!0,onChange:h=>{const m=(h!=null?h:"").trim();m!==(n==null?void 0:n.formula1)&&(a==null||a({...n,formula1:m}))},onFocus:()=>u(!0)})})}const In=["#FFFFFF","#FEE7E7","#FEF0E6","#EFFBD0","#E4F4FE","#E8ECFD","#F1EAFA","#FDE8F3","#E5E5E5","#FDCECE","#FDC49B","#DEF6A2","#9FDAFF","#D0D9FB","#E3D5F6","#FBD0E8","#656565","#FE4B4B","#FF8C51","#8BBB11","#0B9EFB","#3A60F7","#9E6DE3","#F248A6"],Cn=e=>{const{value:t,onChange:r,disabled:n}=e,[a,i]=R.useState(!1);return v.jsx(b.Dropdown,{align:"start",disabled:n,open:a,onOpenChange:i,overlay:v.jsx("div",{className:"univer-box-border univer-grid univer-w-fit univer-grid-cols-6 univer-flex-wrap univer-gap-2 univer-p-1.5",children:In.map(s=>v.jsx("div",{className:b.clsx("univer-box-border univer-size-4 univer-cursor-pointer univer-rounded",b.borderClassName),style:{background:s},onClick:()=>{r(s),i(!1)}},s))}),children:v.jsxs("div",{className:b.clsx("univer-box-border univer-inline-flex univer-h-8 univer-w-16 univer-cursor-pointer univer-items-center univer-justify-between univer-gap-2 univer-rounded-lg univer-bg-white univer-px-2.5 univer-transition-colors univer-duration-200 hover:univer-border-primary-600 dark:!univer-bg-gray-700 dark:!univer-text-white",b.borderClassName),children:[v.jsx("div",{className:"univer-box-border univer-h-4 univer-w-4 univer-rounded univer-text-base",style:{background:t}}),v.jsx(tt,{})]})})},Dt=e=>{const{item:t,commonProps:r,className:n}=e,{onItemChange:a,onItemDelete:i}=r;return v.jsxs("div",{className:b.clsx("univer-flex univer-items-center univer-gap-2",n),children:[!t.isRef&&v.jsx("div",{className:b.clsx("univer-cursor-move","draggableHandle"),children:v.jsx(wt,{})}),v.jsx(Cn,{value:t.color,onChange:s=>{a(t.id,t.label,s)}}),v.jsx(b.Input,{disabled:t.isRef,value:t.label,onChange:s=>{a(t.id,s,t.color)}}),t.isRef?null:v.jsx("div",{className:"univer-ml-1 univer-cursor-pointer univer-rounded univer-text-base hover:univer-bg-gray-200",children:v.jsx(et,{onClick:()=>i(t.id)})})]})};function yn(e){const{value:t,onChange:r=()=>{},unitId:n,subUnitId:a,validResult:i,showError:s,ruleId:d}=e,{formula1:l="",formula2:c=""}=t||{},[u,p]=R.useState(()=>o.isFormulaString(l)?"1":"0"),[h,m]=R.useState(u==="1"?l:"="),[g,O]=R.useState(u==="1"?l:"="),M=V.useDependency(o.LocaleService),f=V.useDependency(X.DataValidatorRegistryService),y=V.useDependency(X.DataValidationModel),_=V.useDependency(S.DataValidationFormulaController),[D,w]=R.useState(()=>c.split(",")),C=f.getValidatorItem(o.DataValidationType.LIST),[E,W]=R.useState([]),[H,P]=R.useState(""),k=s?i==null?void 0:i.formula1:"",F=R.useMemo(()=>y.ruleChange$.pipe(ie.debounceTime(16)),[]),N=V.useObservable(F),x=V.useEvent(r);R.useEffect(()=>{(async()=>{await new Promise(I=>{setTimeout(()=>I(!0),100)});const L=y.getRuleById(n,a,d),Y=L==null?void 0:L.formula1;if(o.isFormulaString(Y)&&C&&L){const I=await C.getListAsync(L,n,a);W(I)}})()},[y,N,C,d,a,n]),R.useEffect(()=>{o.isFormulaString(l)&&l!==g&&(m(l),O(g))},[g,l]);const[K,G]=R.useState(()=>{const L=u!=="1"?S.deserializeListOptions(l):[],Y=c.split(",");return L.map((I,j)=>({label:I,color:Y[j]||ge,isRef:!1,id:o.generateRandomId(4)}))}),ne=(L,Y,I)=>{const j=K.find(z=>z.id===L);j&&(j.label=Y,j.color=I,G([...K]))},re=L=>{const Y=K.findIndex(I=>I.id===L);Y!==-1&&(K.splice(Y,1),G([...K]))},se=c.split(","),ce=R.useMemo(()=>E.map((L,Y)=>({label:L,color:se[Y]||ge,id:`${Y}`,isRef:!0})),[se,E]),q=(L,Y,I)=>{const j=[...D];j[+L]=I,w(j),x({formula1:l,formula2:j.join(",")})},J=()=>{G([...K,{label:"",color:ge,isRef:!1,id:o.generateRandomId(4)}])};R.useEffect(()=>{if(u==="1")return;const L=new Set,Y=[];K.map(I=>({labelList:I.label.split(","),item:I})).forEach(({item:I,labelList:j})=>{j.forEach(z=>{L.has(z)||(L.add(z),Y.push({label:z,color:I.color}))})}),x({formula1:S.serializeListOptions(Y.map(I=>I.label)),formula2:Y.map(I=>I.color===ge?"":I.color).join(",")})},[K,x,u,g,D]);const ve=V.useEvent(async L=>{if(!o.isFormulaString(L)){x==null||x({formula1:"",formula2:c});return}_.getFormulaRefCheck(L)?(x==null||x({formula1:o.isFormulaString(L)?L:"",formula2:c}),P("")):(x==null||x({formula1:"",formula2:c}),m("="),P(M.t("dataValidation.validFail.formulaError")))}),ee=R.useRef(null),[Q,ae]=R.useState(!1);return V.useSidebarClick(L=>{var I;((I=ee.current)==null?void 0:I.isClickOutSide(L))&&ae(!1)}),v.jsxs(v.Fragment,{children:[v.jsx(b.FormLayout,{label:M.t("dataValidation.list.options"),children:v.jsxs(b.RadioGroup,{value:u,onChange:L=>{p(L),m(g),L==="1"&&x({formula1:g==="="?"":g,formula2:D.join(",")})},children:[v.jsx(b.Radio,{value:"0",children:M.t("dataValidation.list.customOptions")}),v.jsx(b.Radio,{value:"1",children:M.t("dataValidation.list.refOptions")})]})}),u==="1"?v.jsxs(b.FormLayout,{error:k||H||void 0,children:[v.jsx(ze.FormulaEditor,{ref:ee,className:b.clsx("univer-box-border univer-h-8 univer-w-full univer-cursor-pointer univer-items-center univer-rounded-lg univer-bg-white univer-pt-2 univer-transition-colors hover:univer-border-primary-600 dark:!univer-bg-gray-700 dark:!univer-text-white [&>div:first-child]:univer-px-2.5 [&>div]:univer-h-5 [&>div]:univer-ring-transparent",b.borderClassName),initValue:h,unitId:n,subUnitId:a,isFocus:Q,isSupportAcrossSheet:!0,onFocus:()=>ae(!0),onChange:(L="")=>{const Y=(L!=null?L:"").trim();O(Y),ve(Y)}}),ce.length>0&&v.jsx("div",{className:"univer-mt-3",children:ce.map(L=>v.jsx(Dt,{className:"univer-mb-3",item:L,commonProps:{onItemChange:q}},L.id))})]}):v.jsx(b.FormLayout,{error:k,children:v.jsxs("div",{className:"-univer-mt-3",children:[v.jsx(b.DraggableList,{list:K,onListChange:G,rowHeight:28,margin:[0,12],draggableHandle:".draggableHandle",itemRender:L=>v.jsx(Dt,{item:L,commonProps:{onItemChange:ne,onItemDelete:re}},L.id),idKey:"id"}),v.jsxs("a",{className:"univer-text-primary univer-flex univer-w-fit univer-cursor-pointer univer-flex-row univer-items-center univer-rounded univer-p-1 univer-px-2 univer-text-sm hover:univer-bg-primary-50 dark:hover:!univer-bg-gray-800",onClick:J,children:[v.jsx(Ct,{className:"univer-mr-1"}),M.t("dataValidation.list.add")]})]})})]})}const wn=[[S.CUSTOM_FORMULA_INPUT_NAME,_n],[S.BASE_FORMULA_INPUT_NAME,mn],[S.LIST_FORMULA_INPUT_NAME,yn],[S.CHECKBOX_FORMULA_INPUT_NAME,Sn]],Dn="LIST_RENDER_MODE_OPTION_INPUT";function je(e){var a;const{value:t,onChange:r}=e,n=V.useDependency(o.LocaleService);return v.jsx(b.FormLayout,{label:n.t("dataValidation.renderMode.label"),children:v.jsxs(b.RadioGroup,{value:`${(a=t.renderMode)!=null?a:o.DataValidationRenderMode.CUSTOM}`,onChange:i=>r({...t,renderMode:+i}),children:[v.jsx(b.Radio,{value:`${o.DataValidationRenderMode.CUSTOM}`,children:n.t("dataValidation.renderMode.chip")}),v.jsx(b.Radio,{value:`${o.DataValidationRenderMode.ARROW}`,children:n.t("dataValidation.renderMode.arrow")}),v.jsx(b.Radio,{value:`${o.DataValidationRenderMode.TEXT}`,children:n.t("dataValidation.renderMode.text")})]})})}je.componentKey=Dn;const En="DATE_SHOW_TIME_OPTION";function Be(e){var a;const{value:t,onChange:r}=e,n=V.useDependency(o.LocaleService);return v.jsx(b.FormLayout,{children:v.jsx(b.Checkbox,{checked:(a=t.bizInfo)==null?void 0:a.showTime,onChange:i=>{r({...t,bizInfo:{...t.bizInfo,showTime:i}})},children:n.t("dataValidation.showTime.label")})})}Be.componentKey=En;var Mn=Object.getOwnPropertyDescriptor,bn=(e,t,r,n)=>{for(var a=n>1?void 0:n?Mn(t,r):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(a=s(a)||a);return a},Re=(e,t)=>(r,n)=>t(r,n,e);const We=6;let nt=class{constructor(e,t,r,n,a,i){this._commandService=e,this._univerInstanceService=t,this._formulaService=r,this._themeService=n,this._renderManagerService=a,this._dataValidationModel=i}_calc(e,t){var c,u,p;const{vt:r,ht:n}=t||{},a=e.endX-e.startX-We*2,i=e.endY-e.startY,s=((c=t==null?void 0:t.fs)!=null?c:10)*1.6;let d=0,l=0;switch(r){case o.VerticalAlign.TOP:l=0;break;case o.VerticalAlign.BOTTOM:l=0+(i-s);break;default:l=0+(i-s)/2;break}switch(n){case o.HorizontalAlign.LEFT:d=We;break;case o.HorizontalAlign.RIGHT:d=We+(a-s);break;default:d=We+(a-s)/2;break}return{left:e.startX+d,top:e.startY+l,width:((u=t==null?void 0:t.fs)!=null?u:10)*1.6,height:((p=t==null?void 0:t.fs)!=null?p:10)*1.6}}calcCellAutoHeight(e){var r;const{style:t}=e;return((r=t==null?void 0:t.fs)!=null?r:10)*1.6}calcCellAutoWidth(e){var r;const{style:t}=e;return((r=t==null?void 0:t.fs)!=null?r:10)*1.6}async _parseFormula(e,t,r){var c,u,p,h,m,g,O,M,f;const{formula1:n=S.CHECKBOX_FORMULA_1,formula2:a=S.CHECKBOX_FORMULA_2}=e,i=await this._formulaService.getRuleFormulaResult(t,r,e.uid),s=S.getFormulaResult((p=(u=(c=i==null?void 0:i[0])==null?void 0:c.result)==null?void 0:u[0])==null?void 0:p[0]),d=S.getFormulaResult((g=(m=(h=i==null?void 0:i[1])==null?void 0:h.result)==null?void 0:m[0])==null?void 0:g[0]),l=S.isLegalFormulaResult(String(s))&&S.isLegalFormulaResult(String(d));return{formula1:o.isFormulaString(n)?S.getFormulaResult((f=(M=(O=i==null?void 0:i[0])==null?void 0:O.result)==null?void 0:M[0])==null?void 0:f[0]):n,formula2:o.isFormulaString(a)?d:a,isFormulaValid:l}}drawWith(e,t){var k,F,N,x;const{style:r,primaryWithCoord:n,unitId:a,subUnitId:i,worksheet:s,row:d,col:l}=t,c=n.isMergedMainCell?n.mergeInfo:n,u=S.getCellValueOrigin(s.getCellRaw(d,l)),p=this._dataValidationModel.getRuleByLocation(a,i,d,l);if(!p)return;const h=this._dataValidationModel.getValidator(p.type);if(!h||!((k=h.skipDefaultFontRender)!=null&&k.call(h,p,u,{unitId:a,subUnitId:i,row:d,column:l})))return;const m=h.parseFormulaSync(p,a,i),{formula1:g}=m,O=this._calc(c,r),{a:M,d:f}=e.getTransform(),y=T.fixLineWidthByScale(O.left,M),_=T.fixLineWidthByScale(O.top,f),D=T.Transform.create().composeMatrix({left:y,top:_,scaleX:1,scaleY:1,angle:0,skewX:0,skewY:0,flipX:!1,flipY:!1}),w=c.endX-c.startX,C=c.endY-c.startY;e.save(),e.beginPath(),e.rect(c.startX,c.startY,w,C),e.clip();const E=D.getMatrix();e.transform(E[0],E[1],E[2],E[3],E[4],E[5]);const W=((F=r==null?void 0:r.fs)!=null?F:10)*1.6,H=String(u)===String(g),P=this._themeService.getColorFromTheme("primary.600");T.CheckboxShape.drawWith(e,{checked:H,width:W,height:W,fill:(x=(N=r==null?void 0:r.cl)==null?void 0:N.rgb)!=null?x:P}),e.restore()}isHit(e,t){const r=t.primaryWithCoord.isMergedMainCell?t.primaryWithCoord.mergeInfo:t.primaryWithCoord,n=this._calc(r,t.style),a=n.top,i=n.top+n.height,s=n.left,d=n.left+n.width,{x:l,y:c}=e;return l<=d&&l>=s&&c<=i&&c>=a}async onPointerDown(e,t){var g;if(t.button===2)return;const{primaryWithCoord:r,unitId:n,subUnitId:a,worksheet:i,row:s,col:d}=e,l=S.getCellValueOrigin(i.getCellRaw(s,d)),c=this._dataValidationModel.getRuleByLocation(n,a,s,d);if(!c)return;const u=this._dataValidationModel.getValidator(c.type);if(!u||!((g=u.skipDefaultFontRender)!=null&&g.call(u,c,l,{unitId:n,subUnitId:a,row:s,column:d})))return;const{formula1:p,formula2:h}=await this._parseFormula(c,n,a),m={range:{startColumn:r.actualColumn,endColumn:r.actualColumn,startRow:r.actualRow,endRow:r.actualRow},value:{v:String(l)===S.transformCheckboxValue(String(p))?h:p,p:null}};this._commandService.executeCommand($.SetRangeValuesCommand.id,m)}onPointerEnter(e,t){var r,n;(n=(r=T.getCurrentTypeOfRenderer(o.UniverInstanceType.UNIVER_SHEET,this._univerInstanceService,this._renderManagerService))==null?void 0:r.mainComponent)==null||n.setCursor(T.CURSOR_TYPE.POINTER)}onPointerLeave(e,t){var r,n;(n=(r=T.getCurrentTypeOfRenderer(o.UniverInstanceType.UNIVER_SHEET,this._univerInstanceService,this._renderManagerService))==null?void 0:r.mainComponent)==null||n.setCursor(T.CURSOR_TYPE.DEFAULT)}};nt=bn([Re(0,o.ICommandService),Re(1,o.IUniverInstanceService),Re(2,o.Inject(S.DataValidationFormulaService)),Re(3,o.Inject(o.ThemeService)),Re(4,o.Inject(T.IRenderManagerService)),Re(5,o.Inject(S.SheetDataValidationModel))],nt);var Vn=Object.getOwnPropertyDescriptor,Rn=(e,t,r,n)=>{for(var a=n>1?void 0:n?Vn(t,r):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(a=s(a)||a);return a},Tn=(e,t)=>(r,n)=>t(r,n,e);U.BaseSheetDataValidatorView=class{constructor(t){A(this,"canvasRender",null);A(this,"dropdownType");A(this,"optionsInput");A(this,"formulaInput",S.LIST_FORMULA_INPUT_NAME);this.injector=t}},U.BaseSheetDataValidatorView=Rn([Tn(0,o.Inject(o.Injector))],U.BaseSheetDataValidatorView);class On extends U.BaseSheetDataValidatorView{constructor(){super(...arguments);A(this,"id",o.DataValidationType.CHECKBOX);A(this,"canvasRender",this.injector.createInstance(nt));A(this,"formulaInput",S.CHECKBOX_FORMULA_INPUT_NAME)}}class Pn extends U.BaseSheetDataValidatorView{constructor(){super(...arguments);A(this,"id",o.DataValidationType.CUSTOM);A(this,"formulaInput",S.CUSTOM_FORMULA_INPUT_NAME)}}const Ln="data-validation.formula-input";class An extends U.BaseSheetDataValidatorView{constructor(){super(...arguments);A(this,"id",o.DataValidationType.DATE);A(this,"formulaInput",Ln);A(this,"optionsInput",Be.componentKey);A(this,"dropdownType",X.DataValidatorDropdownType.DATE)}}class Un extends U.BaseSheetDataValidatorView{constructor(){super(...arguments);A(this,"id",o.DataValidationType.DECIMAL);A(this,"formulaInput",S.BASE_FORMULA_INPUT_NAME)}}const Fn=4;class kn extends T.Shape{static drawWith(t,r){const{fontString:n,info:a,fill:i,color:s}=r,{layout:d,text:l}=a;t.save(),T.Rect.drawWith(t,{width:d.width,height:d.height,radius:Fn,fill:i||ge}),t.font=n,t.fillStyle=s,t.textAlign="center",t.textBaseline="middle";const c=d.width/2,u=d.height/2;t.fillText(l,c,u),t.restore()}}const Nn=6,jn=2,Bn=4,Wn=4,rt=6,xe=6,Ie=14;function xn(e,t){const r=T.FontCache.getTextSize(e,t),n=r.width+Nn*2,{ba:a,bd:i}=r,s=a+i;return{width:n,height:s+jn*2,ba:a}}function at(e,t,r,n){const a=Ie+rt*2,i=r-a,s=n-xe*2,d=e.map(m=>({layout:xn(m,t),text:m})),l={width:0,height:0,items:[]};let c=0;d.forEach((m,g)=>{const{layout:O}=m,{width:M,height:f}=O;c+M<=i&&(l.items.push({...m,left:c}),l.width=c+M,l.height=Math.max(l.height,f),g<d.length-1?c+=M+Bn:c+=M)});const u=[l],p=l.height,h=l.width;return{lines:u,totalHeight:p,contentWidth:i,contentHeight:s,cellAutoHeight:p+xe*2,calcAutoWidth:h+a}}var Hn=Object.getOwnPropertyDescriptor,$n=(e,t,r,n)=>{for(var a=n>1?void 0:n?Hn(t,r):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(a=s(a)||a);return a},He=(e,t)=>(r,n)=>t(r,n,e);const Yn=new Path2D("M3.32201 4.84556C3.14417 5.05148 2.85583 5.05148 2.67799 4.84556L0.134292 1.90016C-0.152586 1.56798 0.0505937 1 0.456301 1L5.5437 1C5.94941 1 6.15259 1.56798 5.86571 1.90016L3.32201 4.84556Z");let it=class{constructor(e,t,r,n){A(this,"zIndex");A(this,"_dropdownInfoMap",new Map);this._commandService=e,this._univerInstanceService=t,this._renderManagerService=r,this._dataValidationModel=n}_ensureMap(e){let t=this._dropdownInfoMap.get(e);return t||(t=new Map,this._dropdownInfoMap.set(e,t)),t}_generateKey(e,t){return`${e}.${t}`}_drawDownIcon(e,t,r,n,a){const i=r-Ie+4;let s=4;switch(a){case o.VerticalAlign.MIDDLE:s=(n-Ie)/2+4;break;case o.VerticalAlign.BOTTOM:s=n-Ie+4;break}e.save(),e.translateWithPrecision(t.startX+i,t.startY+s),e.fillStyle="#565656",e.fill(Yn),e.restore()}drawWith(e,t,r,n){var re,se;const{primaryWithCoord:a,row:i,col:s,style:d,data:l,subUnitId:c}=t,u=a.isMergedMainCell?a.mergeInfo:a,p=l==null?void 0:l.fontRenderExtension,{leftOffset:h=0,rightOffset:m=0,topOffset:g=0,downOffset:O=0}=p||{},M=this._ensureMap(c),f=this._generateKey(i,s),y=this._dataValidationModel.getRuleByLocation(t.unitId,t.subUnitId,i,s);if(!y)return;const _=this._dataValidationModel.getValidator(y.type);if(!_)return;const D={startX:u.startX+h,endX:u.endX-m,startY:u.startY+g,endY:u.endY-O},w=D.endX-D.startX,C=D.endY-D.startY,{cl:E}=d||{},W=(re=typeof E=="object"?E==null?void 0:E.rgb:E)!=null?re:"#000",H=T.getFontStyleString(d!=null?d:void 0),{vt:P,ht:k}=d||{},F=P!=null?P:o.VerticalAlign.MIDDLE,N=(se=S.getCellValueOrigin(l))!=null?se:"",x=_.parseCellValue(N),K=_.getListWithColorMap(y),G=at(x,H,w,C);this._drawDownIcon(e,D,w,C,F),e.save(),e.translateWithPrecision(D.startX,D.startY),e.beginPath(),e.rect(0,0,w-Ie,C),e.clip(),e.translateWithPrecision(rt,xe);const ne=(G.contentHeight-G.totalHeight)/2;e.translateWithPrecision(0,ne),G.lines.forEach((ce,q)=>{e.save();const{height:J,items:ve}=ce;e.translate(0,q*(J+Wn)),ve.forEach(Q=>{e.save(),e.translateWithPrecision(Q.left,0),kn.drawWith(e,{...H,info:Q,color:W,fill:K[Q.text]}),e.restore()}),e.restore()}),e.restore(),M.set(f,{left:D.startX,top:D.startY,width:G.contentWidth+rt+Ie,height:G.contentHeight+xe*2})}calcCellAutoHeight(e){var w;const{primaryWithCoord:t,style:r,data:n,row:a,col:i}=e,s=n==null?void 0:n.fontRenderExtension,{leftOffset:d=0,rightOffset:l=0,topOffset:c=0,downOffset:u=0}=s||{},p=t.isMergedMainCell?t.mergeInfo:t,h={startX:p.startX+d,endX:p.endX-l,startY:p.startY+c,endY:p.endY-u},m=this._dataValidationModel.getRuleByLocation(e.unitId,e.subUnitId,a,i);if(!m)return;const g=this._dataValidationModel.getValidator(m.type);if(!g)return;const O=h.endX-h.startX,M=h.endY-h.startY,f=(w=S.getCellValueOrigin(n))!=null?w:"",y=g.parseCellValue(f),_=T.getFontStyleString(r!=null?r:void 0);return at(y,_,O,M).cellAutoHeight}calcCellAutoWidth(e){var w;const{primaryWithCoord:t,style:r,data:n,row:a,col:i}=e,s=n==null?void 0:n.fontRenderExtension,{leftOffset:d=0,rightOffset:l=0,topOffset:c=0,downOffset:u=0}=s||{},p=t.isMergedMainCell?t.mergeInfo:t,h={startX:p.startX+d,endX:p.endX-l,startY:p.startY+c,endY:p.endY-u},m=this._dataValidationModel.getRuleByLocation(e.unitId,e.subUnitId,a,i);if(!m)return;const g=this._dataValidationModel.getValidator(m.type);if(!g)return;const O=h.endX-h.startX,M=h.endY-h.startY,f=(w=S.getCellValueOrigin(n))!=null?w:"",y=g.parseCellValue(f),_=T.getFontStyleString(r!=null?r:void 0);return at(y,_,O,M).calcAutoWidth}isHit(e,t){const{primaryWithCoord:r}=t,n=r.isMergedMainCell?r.mergeInfo:r,{endX:a}=n,{x:i}=e;return i>=a-Ie&&i<=a}onPointerDown(e,t){if(t.button===2)return;const{unitId:r,subUnitId:n,row:a,col:i}=e,s={unitId:r,subUnitId:n,row:a,column:i};this._commandService.executeCommand(Fe.id,s)}onPointerEnter(e,t){var r,n;return(n=(r=T.getCurrentTypeOfRenderer(o.UniverInstanceType.UNIVER_SHEET,this._univerInstanceService,this._renderManagerService))==null?void 0:r.mainComponent)==null?void 0:n.setCursor(T.CURSOR_TYPE.POINTER)}onPointerLeave(e,t){var r,n;return(n=(r=T.getCurrentTypeOfRenderer(o.UniverInstanceType.UNIVER_SHEET,this._univerInstanceService,this._renderManagerService))==null?void 0:r.mainComponent)==null?void 0:n.setCursor(T.CURSOR_TYPE.DEFAULT)}};it=$n([He(0,o.ICommandService),He(1,o.IUniverInstanceService),He(2,o.Inject(T.IRenderManagerService)),He(3,o.Inject(S.SheetDataValidationModel))],it);class Xn extends U.BaseSheetDataValidatorView{constructor(){super(...arguments);A(this,"id",o.DataValidationType.LIST_MULTIPLE);A(this,"canvasRender",this.injector.createInstance(it));A(this,"dropdownType",X.DataValidatorDropdownType.MULTIPLE_LIST)}}var zn=Object.getOwnPropertyDescriptor,Kn=(e,t,r,n)=>{for(var a=n>1?void 0:n?zn(t,r):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(a=s(a)||a);return a},Le=(e,t)=>(r,n)=>t(r,n,e);const Ce=6,$e=4,de=14,Et=2,he=6,Te=3,ot=4,Zn="#565656",Mt=new Path2D("M3.32201 4.84556C3.14417 5.05148 2.85583 5.05148 2.67799 4.84556L0.134292 1.90016C-0.152586 1.56798 0.0505937 1 0.456301 1L5.5437 1C5.94941 1 6.15259 1.56798 5.86571 1.90016L3.32201 4.84556Z");function bt(e,t,r,n,a,i,s=!0){let d=0;const l=s?Te:0;switch(a){case o.VerticalAlign.BOTTOM:d=t-n-l;break;case o.VerticalAlign.MIDDLE:d=(t-n)/2;break;default:d=l;break}d=Math.max(Te,d);let c=0;switch(i){case o.HorizontalAlign.CENTER:c=(e-r)/2;break;case o.HorizontalAlign.RIGHT:c=e-r;break}return c=Math.max(he,c),{paddingLeft:c,paddingTop:d}}let st=class{constructor(e,t,r,n,a){A(this,"_dropdownInfoMap",new Map);A(this,"zIndex");this._univerInstanceService=e,this._localeService=t,this._commandService=r,this._renderManagerService=n,this._dataValidationModel=a}_ensureMap(e){let t=this._dropdownInfoMap.get(e);return t||(t=new Map,this._dropdownInfoMap.set(e,t)),t}_generateKey(e,t){return`${e}.${t}`}_drawDownIcon(e,t,r,n,a,i,s){const{t:d=o.DEFAULT_STYLES.pd.t,b:l=o.DEFAULT_STYLES.pd.b}=s,c=r-de;let u;switch(i){case o.VerticalAlign.MIDDLE:u=(n-$e)/2;break;case o.VerticalAlign.BOTTOM:u=n-l-a-Te+(a/2-$e/2);break;default:u=d+Te+(a/2-$e/2);break}e.save(),e.translateWithPrecision(t.startX+c,t.startY+u),e.fillStyle="#565656",e.fill(Mt),e.restore()}drawWith(e,t,r){var K,G,ne,re,se,ce;const{primaryWithCoord:n,row:a,col:i,style:s,data:d,subUnitId:l}=t,c=n.isMergedMainCell?n.mergeInfo:n,u=this._dataValidationModel.getRuleByLocation(t.unitId,t.subUnitId,a,i);if(!u)return;const p=this._dataValidationModel.getValidator(u.type);if(!p)return;const h=d==null?void 0:d.fontRenderExtension,{leftOffset:m=0,rightOffset:g=0,topOffset:O=0,downOffset:M=0}=h||{};if(!u||!p||!p||p.id.indexOf(o.DataValidationType.LIST)!==0||!p.skipDefaultFontRender(u))return;const f={startX:c.startX+m,endX:c.endX-g,startY:c.startY+O,endY:c.endY-M},y=f.endX-f.startX,_=f.endY-f.startY,D=this._ensureMap(l),w=this._generateKey(a,i),C=p.getListWithColor(u),E=S.getCellValueOrigin(d),W=`${E!=null?E:""}`,H=C.find(q=>q.label===W);let{tb:P,vt:k,ht:F,pd:N}=s||{};P=P!=null?P:o.WrapStrategy.WRAP,k=k!=null?k:o.VerticalAlign.BOTTOM,F=F!=null?F:o.DEFAULT_STYLES.ht,N=N!=null?N:o.DEFAULT_STYLES.pd;const x=T.getFontStyleString(s).fontCache;if(u.renderMode===o.DataValidationRenderMode.ARROW){const{l:q=o.DEFAULT_STYLES.pd.l,t:J=o.DEFAULT_STYLES.pd.t,r:ve=o.DEFAULT_STYLES.pd.r,b:ee=o.DEFAULT_STYLES.pd.b}=N,Q=y-q-ve-de-4,ae=new T.DocSimpleSkeleton(W,x,P===o.WrapStrategy.WRAP,Q,1/0);ae.calculate();const L=ae.getTotalWidth(),Y=ae.getTotalHeight(),{paddingTop:I,paddingLeft:j}=bt(Q,_-J-ee,L,Y,k,F,!0);this._drawDownIcon(e,f,y,_,Y,k,N),e.save(),e.translateWithPrecision(f.startX+q,f.startY+J),e.beginPath(),e.rect(0,0,y-q-ve,_-J-ee),e.clip(),e.translateWithPrecision(0,I),e.save(),e.translateWithPrecision(Ce,0),e.beginPath(),e.rect(0,0,Q,Y),e.clip(),T.Text.drawWith(e,{text:W,fontStyle:x,width:Q,height:Y,color:(K=s==null?void 0:s.cl)==null?void 0:K.rgb,strokeLine:!!((G=s==null?void 0:s.st)!=null&&G.s),underline:!!((ne=s==null?void 0:s.ul)!=null&&ne.s),warp:P===o.WrapStrategy.WRAP,hAlign:o.HorizontalAlign.LEFT},ae),e.translateWithPrecision(j,0),e.restore(),e.restore(),D.set(w,{left:f.endX+q+r.rowHeaderWidth-de,top:f.startY+J+r.columnHeaderHeight,width:de,height:_-J-ee})}else{e.save(),e.translateWithPrecision(f.startX,f.startY),e.beginPath(),e.rect(0,0,y,_),e.clip();const q=y-he*2-Ce-de-4,J=new T.DocSimpleSkeleton(W,x,P===o.WrapStrategy.WRAP,q,1/0);J.calculate();const ve=J.getTotalWidth(),ee=J.getTotalHeight(),Q=ee+Et*2,ae=Math.max(y-he*2,1),{paddingTop:L,paddingLeft:Y}=bt(ae,_,ve,Q,k,F);e.translateWithPrecision(he,L),T.Rect.drawWith(e,{width:ae,height:Q,fill:(H==null?void 0:H.color)||ge,radius:ot}),e.save(),e.translateWithPrecision(Ce,Et),e.beginPath(),e.rect(0,0,q,ee),e.clip(),e.translateWithPrecision(Y,0),T.Text.drawWith(e,{text:W,fontStyle:x,width:q,height:ee,color:(re=s==null?void 0:s.cl)==null?void 0:re.rgb,strokeLine:!!((se=s==null?void 0:s.st)!=null&&se.s),underline:!!((ce=s==null?void 0:s.ul)!=null&&ce.s),warp:P===o.WrapStrategy.WRAP,hAlign:o.HorizontalAlign.LEFT},J),e.restore(),e.translateWithPrecision(q+Ce+4,(ee-$e)/2),e.fillStyle=Zn,e.fill(Mt),e.restore(),D.set(w,{left:f.startX+he+r.rowHeaderWidth,top:f.startY+L+r.columnHeaderHeight,width:ae,height:Q})}}calcCellAutoHeight(e){const{primaryWithCoord:t,style:r,data:n,row:a,col:i}=e,s=t.isMergedMainCell?t.mergeInfo:t,d=n==null?void 0:n.fontRenderExtension,{leftOffset:l=0,rightOffset:c=0,topOffset:u=0,downOffset:p=0}=d||{},h=this._dataValidationModel.getRuleByLocation(e.unitId,e.subUnitId,a,i);if(!h||h.renderMode===o.DataValidationRenderMode.TEXT)return;const m={startX:s.startX+l,endX:s.endX-c,startY:s.startY+u,endY:s.endY-p},g=m.endX-m.startX,O=S.getCellValueOrigin(n),M=`${O!=null?O:""}`;let{tb:f,pd:y}=r||{};const{t:_=o.DEFAULT_STYLES.pd.t,b:D=o.DEFAULT_STYLES.pd.b}=y!=null?y:{};if(f=f!=null?f:o.WrapStrategy.WRAP,h.renderMode===o.DataValidationRenderMode.ARROW){const w=g-de,C=new T.DocSimpleSkeleton(M,T.getFontStyleString(r).fontCache,f===o.WrapStrategy.WRAP,w,1/0);return C.calculate(),C.getTotalHeight()+_+D+Te*2}else{const w=Math.max(g-he*2-Ce-de,10),C=new T.DocSimpleSkeleton(M,T.getFontStyleString(r).fontCache,f===o.WrapStrategy.WRAP,w,1/0);return C.calculate(),C.getTotalHeight()+Te*2+4}}calcCellAutoWidth(e){const{primaryWithCoord:t,style:r,data:n,row:a,col:i}=e,s=t.isMergedMainCell?t.mergeInfo:t,d=n==null?void 0:n.fontRenderExtension,{leftOffset:l=0,rightOffset:c=0,topOffset:u=0,downOffset:p=0}=d||{},h=this._dataValidationModel.getRuleByLocation(e.unitId,e.subUnitId,a,i);if(!h||h.renderMode===o.DataValidationRenderMode.TEXT)return;const m={startX:s.startX+l,endX:s.endX-c,startY:s.startY+u,endY:s.endY-p},g=m.endX-m.startX,O=S.getCellValueOrigin(n),M=`${O!=null?O:""}`;let{tb:f,pd:y}=r||{};const{l:_=o.DEFAULT_STYLES.pd.l,r:D=o.DEFAULT_STYLES.pd.r}=y!=null?y:{};f=f!=null?f:o.WrapStrategy.WRAP;let w=he*2+de;switch(h.renderMode){case o.DataValidationRenderMode.ARROW:w=de+he*2+D+_;break;case o.DataValidationRenderMode.CUSTOM:w=de+he*2+Ce*2+D+_+ot/2+1;break;default:w=de+he*2+Ce*2+D+_+ot/2+1}const C=g-w,E=new T.DocSimpleSkeleton(M,T.getFontStyleString(r).fontCache,f===o.WrapStrategy.WRAP,C,1/0);return E.calculate(),E.getTotalWidth()+w}isHit(e,t){const{subUnitId:r,row:n,col:a}=t,s=this._ensureMap(r).get(this._generateKey(n,a)),d=this._dataValidationModel.getRuleByLocation(t.unitId,t.subUnitId,n,a);if(!d||!s||d.renderMode===o.DataValidationRenderMode.TEXT)return!1;const{top:l,left:c,width:u,height:p}=s,{x:h,y:m}=e;return h>=c&&h<=c+u&&m>=l&&m<=l+p}onPointerDown(e,t){if(t.button===2)return;const{unitId:r,subUnitId:n,row:a,col:i}=e,s={unitId:r,subUnitId:n,row:a,column:i};this._commandService.executeCommand(Fe.id,s)}onPointerEnter(e,t){var r,n;(n=(r=T.getCurrentTypeOfRenderer(o.UniverInstanceType.UNIVER_SHEET,this._univerInstanceService,this._renderManagerService))==null?void 0:r.mainComponent)==null||n.setCursor(T.CURSOR_TYPE.POINTER)}onPointerLeave(e,t){var r,n;(n=(r=T.getCurrentTypeOfRenderer(o.UniverInstanceType.UNIVER_SHEET,this._univerInstanceService,this._renderManagerService))==null?void 0:r.mainComponent)==null||n.setCursor(T.CURSOR_TYPE.DEFAULT)}};st=Kn([Le(0,o.IUniverInstanceService),Le(1,o.Inject(o.LocaleService)),Le(2,o.ICommandService),Le(3,o.Inject(T.IRenderManagerService)),Le(4,o.Inject(S.SheetDataValidationModel))],st);class Gn extends U.BaseSheetDataValidatorView{constructor(){super(...arguments);A(this,"id",o.DataValidationType.LIST);A(this,"canvasRender",this.injector.createInstance(st));A(this,"dropdownType",X.DataValidatorDropdownType.LIST);A(this,"optionsInput",je.componentKey);A(this,"formulaInput",S.LIST_FORMULA_INPUT_NAME)}}class qn extends U.BaseSheetDataValidatorView{constructor(){super(...arguments);A(this,"id",o.DataValidationType.TEXT_LENGTH);A(this,"formulaInput",S.BASE_FORMULA_INPUT_NAME)}}class Jn extends U.BaseSheetDataValidatorView{constructor(){super(...arguments);A(this,"id",o.DataValidationType.WHOLE);A(this,"formulaInput",S.BASE_FORMULA_INPUT_NAME)}}var Qn=Object.getOwnPropertyDescriptor,er=(e,t,r,n)=>{for(var a=n>1?void 0:n?Qn(t,r):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(a=s(a)||a);return a},lt=(e,t)=>(r,n)=>t(r,n,e);let Oe=class extends o.RxDisposable{constructor(e,t,r){super(),this._injector=e,this._componentManger=t,this._dataValidatorRegistryService=r,this._initComponents(),this._registerValidatorViews()}_initComponents(){[["DataValidationIcon",It],[Ue,fn],[je.componentKey,je],[Be.componentKey,Be],...wn].forEach(([e,t])=>{this.disposeWithMe(this._componentManger.register(e,t))})}_registerValidatorViews(){[Un,Jn,qn,An,On,Gn,Xn,Pn].forEach(e=>{const t=this._injector.createInstance(e),r=this._dataValidatorRegistryService.getValidatorItem(t.id);r&&(r.formulaInput=t.formulaInput,r.canvasRender=t.canvasRender,r.dropdownType=t.dropdownType,r.optionsInput=t.optionsInput)})}};Oe=er([lt(0,o.Inject(o.Injector)),lt(1,o.Inject(V.ComponentManager)),lt(2,o.Inject(X.DataValidatorRegistryService))],Oe);var tr=Object.getOwnPropertyDescriptor,nr=(e,t,r,n)=>{for(var a=n>1?void 0:n?tr(t,r):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(a=s(a)||a);return a},dt=(e,t)=>(r,n)=>t(r,n,e);const rr="SHEET_DATA_VALIDATION_UI_PLUGIN";U.UniverSheetsDataValidationMobileUIPlugin=(Ye=class extends o.Plugin{constructor(t=Ae,r,n,a){super(),this._config=t,this._injector=r,this._commandService=n,this._configService=a;const{menu:i,...s}=o.merge({},Ae,this._config);i&&this._configService.setConfig("menu",i,{merge:!0}),this._configService.setConfig(Ke,s)}onStarting(){[[ue],[fe],[Pe],[Ee],[Ve],[be],[Me],[Oe]].forEach(t=>{this._injector.add(t)}),[ke,Fe,vt,Ge,me,ht].forEach(t=>{this._commandService.registerCommand(t)})}onReady(){this._injector.get(Me),this._injector.get(be),this._injector.get(T.IRenderManagerService).registerRenderModule(o.UniverInstanceType.UNIVER_SHEET,[Ne])}onRendered(){this._injector.get(Oe),this._injector.get(Ve)}onSteady(){this._injector.get(Ee)}},A(Ye,"pluginName",rr),A(Ye,"type",o.UniverInstanceType.UNIVER_SHEET),Ye),U.UniverSheetsDataValidationMobileUIPlugin=nr([dt(1,o.Inject(o.Injector)),dt(2,o.ICommandService),dt(3,o.IConfigService)],U.UniverSheetsDataValidationMobileUIPlugin);var ar=Object.defineProperty,ir=Object.getOwnPropertyDescriptor,or=(e,t,r)=>t in e?ar(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,sr=(e,t,r,n)=>{for(var a=n>1?void 0:n?ir(t,r):t,i=e.length-1,s;i>=0;i--)(s=e[i])&&(a=s(a)||a);return a},ct=(e,t)=>(r,n)=>t(r,n,e),Vt=(e,t,r)=>or(e,typeof t!="symbol"?t+"":t,r);const lr="SHEET_DATA_VALIDATION_UI_PLUGIN";U.UniverSheetsDataValidationUIPlugin=class extends o.Plugin{constructor(t=Ae,r,n,a){super(),this._config=t,this._injector=r,this._commandService=n,this._configService=a;const{menu:i,...s}=o.merge({},Ae,this._config);i&&this._configService.setConfig("menu",i,{merge:!0}),this._configService.setConfig(Ke,s)}onStarting(){[[ue],[fe],[Pe],[Ee],[Ve],[be],[Me],[we],[Oe]].forEach(t=>{this._injector.add(t)}),[ke,Fe,vt,Ge,me,ht].forEach(t=>{this._commandService.registerCommand(t)})}onReady(){this._injector.get(Me),this._injector.get(be),this._injector.get(we),this._injector.get(Pe),this._injector.get(T.IRenderManagerService).registerRenderModule(o.UniverInstanceType.UNIVER_SHEET,[Ne])}onRendered(){this._injector.get(Oe),this._injector.get(Ve)}onSteady(){this._injector.get(Ee)}},Vt(U.UniverSheetsDataValidationUIPlugin,"pluginName",lr),Vt(U.UniverSheetsDataValidationUIPlugin,"type",o.UniverInstanceType.UNIVER_SHEET),U.UniverSheetsDataValidationUIPlugin=sr([o.DependentOn(S.UniverSheetsDataValidationPlugin),ct(1,o.Inject(o.Injector)),ct(2,o.ICommandService),ct(3,o.IConfigService)],U.UniverSheetsDataValidationUIPlugin),Object.defineProperty(U,Symbol.toStringTag,{value:"Module"})});
